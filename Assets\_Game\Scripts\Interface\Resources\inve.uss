/* 
 * MAIN LAYOUT CONTAINERS
 */
.root-container {
    flex-grow: 1;
    flex-direction: column;
    justify-content: flex-end;
    align-items: center;
    -unity-font-definition: resource('SpaceGrotesk-Medium');
}

.main-container {
    flex-grow: 0;
    flex-shrink: 0;
    flex-direction: row;
    width: 100%;
    justify-content: center;
    margin-bottom: 0;
}

/* 
 * PANELS
 */
.panel {
    background-color: rgb(0, 0, 0);
    border-width: 5px;
    border-color: rgb(14, 14, 14);
    flex-grow: 0;
}

.panel--secondary {
    border-left-width: 0px;
}

#StashContainer {
    border-bottom-width: 0px;
    width: 100%;
}

#StashMainContainer {
    width: 446px;
}

/* 
 * HEADERS
 */
.header {
    width: 100%;
    flex-direction: row;
    justify-content: space-between;
    padding: 16px 24px 16px 24px;
    background-color: rgb(0, 0, 0);
    align-items: center;
    border-bottom-width: 5px;
    border-bottom-color: rgb(14, 14, 14);
}

.header__icon {
    width: 16px;
    height: 16px;
}

.header__title {
    color: rgba(197, 197, 197, 0.25);
    font-size: 16px;
    -unity-font-definition: resource('SpaceGrotesk-Bold');
}

.header__money {
    color: rgba(197, 197, 197, 0.25);
    font-size: 16px;
    -unity-font-definition: resource('SpaceGrotesk-Bold');
}

/* Money counter digit styling */
.digit-zero {
    color: rgb(70, 70, 70);
}

.digit-value {
    color: rgb(150, 150, 150);
}

.currency-symbol {
    color: rgb(70, 70, 70);
}

/* 
 * EQUIPMENT AREA
 */
.equipment-area {
    display: flex;
    flex-direction: row;
    justify-content: center;
    padding: 24px 24px 0px 24px;
    background-color: rgb(10, 10, 10);
    min-height: 136px;
    align-items: center;
}

#EquipmentArea {
    background-color: rgb(0, 0, 0);
}

.equipment-slots {
    flex-direction: row;
    justify-content: space-between;
    width: 100%;
}

/* Base styles for all equipment slots */
#HeadSlot, #ChestSlot, #BagSlot, .equipment-slot {
    width: 112px;
    height: 112px;
    min-width: 112px;
    min-height: 112px;
    max-width: 112px;
    max-height: 112px;
    margin-right: 0;
    flex-shrink: 0;
    flex-grow: 0;
    transition-property: background-color, border-color;
    transition-duration: 0.1s;
    transition-timing-function: ease;
}

/* Empty equipment slot style */
.equipment-slot.empty {
    border-width: 0;
    background-color: rgb(20, 20, 20);
}

/* Filled equipment slot style */
.equipment-slot.filled {
    border-width: 1px;
    border-color: rgb(60, 60, 60);
    background-color: rgb(42, 42, 42);
}

/* Placeholder images in empty slots */
.equipment-slot.empty .equipment-slot-img {
    width: 70%;
    height: 70%;
    align-self: center;
    -unity-background-scale-mode: scale-to-fit;
}

/* Hide placeholder images in filled slots */
.equipment-slot.filled .equipment-slot-img {
    width: 70%;
    height: 70%;
    background-image: none;
}

/* Hover effects */
.equipment-slot.filled:hover {
    background-color: rgb(50, 50, 50);
    border-color: rgb(80, 80, 80);
}

.equipment-slot.empty:hover {
    background-color: rgb(30, 30, 30);
}

/* Valid target highlight */
.equipment-slot.valid-target {
    background-color: rgba(0, 150, 60, 0.3);
    border-width: 1px;
    border-color: rgb(0, 150, 60);
}

.equipment-slot-img {
    width: 100%;
    height: 100%;
}

/* 
 * SCROLL VIEWS
 */
.scroll-view {
    flex-grow: 1;
    padding: 0;
    overflow: hidden;
}

#StashScrollView {
    width: auto;
    background-color: rgb(0, 0, 0);
    padding: 24px;
    overflow: hidden;
}

#Inv {
    align-self: center;
    background-color: rgb(0, 0, 0);
    width: 100%;
    padding: 24px;
}

/* 
 * PLACEHOLDER FOR EMPTY INVENTORY
 */
.inventory-placeholder {
    height: 210px;
    width: 100%;
    background-color: rgb(0, 0, 0);
    justify-content: center;
    align-items: center;
    padding: 16px;
    border-width: 1px;
    border-color: rgb(26, 26, 26);
    border-style: dotted;
}

.inventory-placeholder-text {
    color: rgb(100, 100, 100);
    -unity-text-align: middle-center;
    font-size: 14px;
    padding: 16px;
}

/* 
 * GRID CONTAINERS
 */
.grid-container {
    position: relative;
    width: 100%;
    height: 100%;
    flex-direction: row;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: rgb(0, 0, 0);
}

#StashGrid, #InvGrid {
    display: grid;
    grid-template-columns: repeat(7, 48px);
    grid-template-rows: repeat(3, 48px);
    grid-gap: 1px;
    align-self: center;
    justify-self: center;
    background-color: rgb(0, 0, 0);
}

#ShopGrid {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    width: 100%;
    grid-template-columns: repeat(6, 64px);
    grid-template-rows: repeat(5, 64px);
    grid-gap: 2px;
    align-self: center;
    justify-self: center;
    padding: 24px !important;
    background-color: rgb(0, 0, 0) !important;
}

#SellGrid {
    position: relative;
    width: 100%;
    height: 100%;
    align-self: center;
    justify-self: center;
    background-color: rgb(0, 0, 0);
    padding: 24px;
}

#ShopScrollView {
    overflow: hidden;
}

/* 
 * ITEM SLOTS
 */
.inventory-item {
    border-width: 0;
    background-color: rgb(0, 0, 0);
    width: 48px;
    height: 48px;
    align-items: center;
    justify-content: center;
}

.inventory-slot {
    border: 1px solid rgb(0, 0, 0);
}

/* Shop items */
.shop-item {
    border-width: 1px;
    border-color: rgb(60, 60, 60);
    background-color: rgb(25, 25, 25);
    width: 60px;
    height: 60px;
    align-items: center;
    justify-content: center;
    position: absolute;
}

.shop-item--selected {
    border-color: rgb(0, 150, 60);
    background-color: rgb(35, 35, 35);
}

.shop-item-icon {
    width: 80%;
    height: 80%;
    align-self: center;
}

.shop-item-price {
    position: absolute;
    bottom: 2px;
    right: 2px;
    color: rgb(255, 255, 255);
    font-size: 10px;
    background-color: rgba(0, 0, 0, 0.5);
    padding: 1px 3px;
    border-radius: 2px;
}

.price-cannot-afford {
    color: rgb(153, 153, 153);
}

/* Base item slot */
.ItemSlot {
    background-color: rgb(25, 25, 25);
    border-width: 1px;
    border-color: rgb(0, 0, 0);
    width: 64px;
    height: 64px;
    display: flex;
    justify-content: center;
    align-items: center;
}

/* Item slot with item */
.ItemSlotWithItem {
    background-color: rgb(30, 30, 30);
    border-width: 1px;
    border-color: rgb(0, 0, 0);
    width: 64px;
    height: 64px;
    display: flex;
    justify-content: center;
    align-items: center;
    transition-property: all;
    transition-duration: 0.15s;
    transition-timing-function: ease-out;
    will-change: background-color, border-color;
}

.ItemSlotWithItem:hover {
    background-color: rgb(40, 40, 40);
    border-color: rgb(100, 100, 100);
    transition-property: background-color, border-color;
    transition-duration: 0s;
    will-change: background-color, border-color;
}

.ItemSlotWithItem:active {
    background-color: rgb(50, 50, 50);
    border-color: rgb(120, 120, 120);
}

.slot-hovered {
    background-color: rgb(40, 40, 40);
    border-color: rgb(100, 100, 100);
}

/* Buy/Sell slots */
.BuySlot {
    background-color: rgb(25, 25, 25);
    border-width: 1px;
    border-color: rgb(40, 40, 40);
    width: 64px;
    height: 64px;
}

.BuySlotWithItem {
    background-color: rgb(25, 25, 25);
    border-width: 1px;
    border-color: rgb(0, 120, 50);
    width: 64px;
    height: 64px;
}

.SellSlot {
    background-color: rgb(30, 30, 30);
    border-width: 1px;
    border-color: rgb(60, 60, 60);
    margin: 0;
    padding: 0;
    width: 64px;
    height: 64px;
}

.SellSlot:hover {
    background-color: rgb(40, 40, 40);
    border-color: rgb(80, 80, 80);
}

.SellSlotWithItem {
    background-color: rgb(35, 35, 35);
    border-width: 1px;
    border-color: rgb(100, 100, 100);
}

.SellSlotWithItem:hover {
    background-color: rgb(45, 45, 45);
    border-color: rgb(120, 120, 120);
}

.drag-hover-valid {
    background-color: rgba(0, 150, 60, 0.3);
    border-color: rgb(0, 150, 60);
}

/* 
 * STATS SECTION
 */
.stats {
    padding: 8px 16px;
    flex-direction: row;
    background-color: rgb(10, 10, 10);
}

.stats__label {
    -unity-font-definition: resource('SpaceGrotesk-Bold');
    margin-right: 24px;
}

.stats__weight {
    color: rgb(244, 162, 97);
}

.stats__energy {
    color: rgb(97, 182, 244);
}

/* 
 * ENERGY CONTAINER AND BATTERY
 */
.energy-container {
    padding: 8px 24px 8px 24px;
    background-color: rgb(0, 0, 0) !important; /* Pure black, not gray */
    height: 40px;
    justify-content: center;
    border-top-width: 5px;
    border-color: rgb(14, 14, 14);
}

.battery-indicator {
    height: 20px;
    flex-direction: row;
    align-items: center;
    background-color: transparent !important;
}

.battery-icon {
    width: 24px;
    height: 24px;
    -unity-background-scale-mode: scale-to-fit;
    background-color: transparent; /* Ensure icon has no background */
}

.battery-bar {
    flex-grow: 1;
    height: 4px;
    margin-left: 16px;
    background-color: transparent;
    flex-direction: row;
    position: relative;
}

.battery-fill {
    height: 100%;
    position: absolute;
    display: flex;
}

.battery-fill--low {
    background-color: rgb(55, 55, 55);
    width: 15%;
    left: 0;
}

.battery-fill--medium {
    background-color: rgb(94, 94, 94);
    width: 35%;
    left: 15%;
}

.battery-fill--high {
    background-color: rgb(169, 169, 169);
    width: 50%;
    left: 50%;
}

/* 
 * SHOP STYLING
 */
.shop-controls {
    flex-direction: row;
    align-items: right;
    justify-content: right;
    padding: 24px 24px 0px 24px;
    border-bottom-width: 0px;
}

.shop-footer {
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    background-color: rgb(0, 0, 0);
    padding: 16px;
    border-top-width: 5px;
    border-color: rgb(14, 14, 14);
}

.value-info {
    flex-direction: row;
    align-items: center;
}

/* 
 * BUTTONS
 */
.pixel-button {
    width: 60px;
    height: 30px;
    background-color: rgba(33, 33, 33, 0.31);
    color: rgb(159, 159, 159);
    border-width: 0px;
    margin: 0px;
    -unity-font-definition: resource('SpaceGrotesk-Medium');
}

.pixel-button:hover {
    background-color: rgba(33, 33, 33, 0.5);
    color: white;
}

.pixel-button:active {
    background-color: rgba(33, 33, 33, 0.7);
}

.pixel-button:disabled {
    background-color: rgba(255, 255, 255, 0.10);
    color: rgba(255, 255, 255, 0.40);
}

.pixel-button--full-width {
    width: 100%;
    margin-top: 16px;
}

.pixel-button--mode {
    width: 80px;
    height: 36px;
}

.pixel-button--active {
    background-color: rgba(66, 135, 255, 0.25);
    color: rgb(66, 135, 255);
}

#BuyModeButton.pixel-button--active {
    background-color: rgba(66, 135, 255, 0.25);
    color: rgb(66, 135, 255);
}

#SellModeButton {
    margin-left: 8px;
}

#SellModeButton.pixel-button--active {
    background-color: rgba(255, 132, 66, 0.25);
    color: rgb(255, 132, 66);
}

.pixel-button--action {
    background-color: rgba(66, 135, 255, 0.25);
    color: rgb(66, 135, 255);
    height: 36px;
}

.pixel-button--action:hover {
    background-color: rgba(66, 135, 255, 0.4);
    color: rgb(66, 135, 255);
}

.pixel-button--buy {
    background-color: rgba(66, 135, 255, 0.25);
    color: rgb(66, 135, 255);
    height: 36px;
}

.pixel-button--buy:hover {
    background-color: rgba(66, 135, 255, 0.4);
    color: rgb(66, 135, 255);
}

.pixel-button--buy:active {
    background-color: rgba(66, 135, 255, 0.6);
    color: rgb(66, 135, 255);
}

.pixel-button--sell {
    background-color: rgba(255, 132, 66, 0.25);
    color: rgb(255, 132, 66);
    height: 36px;
}

.pixel-button--sell:hover {
    background-color: rgba(255, 132, 66, 0.4);
    color: rgb(255, 132, 66);
}

.pixel-button--sell:active {
    background-color: rgba(255, 132, 66, 0.6);
    color: rgb(255, 132, 66);
}

/* 
 * TRADE MODE
 */
.mode-container {
    width: 100%;
    flex-grow: 1;
    flex-shrink: 0;
    flex-direction: column;
    min-height: 0;
}

.grid-section {
    padding: 8px;
    background-color: rgb(15, 15, 15);
}

.trade-details {
    padding: 8px;
    background-color: rgb(24, 24, 24);
    flex-direction: row;
    margin-top: 8px;
}

.trade-info {
    background-color: rgb(26, 26, 26);
    flex-grow: 1;
    justify-content: space-between;
    padding: 16px;
}

/* 
 * LABELS
 */
.label-text {
    color: rgb(255, 0, 0);
}

.label-value {
    color: rgb(255, 255, 255);
}

/* 
 * INSTRUCTIONS
 */
.instructions {
    background-color: rgb(30, 30, 30);
    -unity-text-align: right;
}

.instructions-container {
    width: 100%;
    padding: 0px 16px;
    border-top-width: 5px;
    border-top-color: rgb(14, 14, 14);
}

.instructions-wrapper {
    width: 100%;
}

.instruction-text {
    color: rgb(51, 51, 51);
    -unity-text-align: right;
    -unity-font-definition: resource('SpaceGrotesk-Medium');
}

.no-items-message {
    color: rgb(128, 128, 128);
    -unity-text-align: middle-center;
    width: 100%;
    height: 100px;
}

/* 
 * TOOLTIPS
 */
#Tooltip {
    position: absolute;
    background-color: rgb(0, 0, 0);
    border-width: 5px;
    border-color: rgb(14, 14, 14);
    padding-top: 16px;
    padding-bottom: 16px;
    padding-left: 24px;
    padding-right: 24px;
    min-width: 250px;
    max-width: 350px;
    display: none;
    flex-direction: column;
}

#Tooltip TextField {
    margin-bottom: 10px;
    font-size: 16px;
    -unity-font-style: bold;
    background-color: rgb(25, 25, 25);
    color: rgb(255, 255, 255);
    border-width: 1px;
    border-color: rgb(60, 60, 60);
    padding-top: 6px;
    padding-bottom: 6px;
    padding-left: 8px;
    padding-right: 8px;
}

#Tooltip Label {
    font-size: 13px;
    color: rgb(197, 197, 197);
    white-space: normal;
    margin-top: 4px;
}

.tooltip-container {
    position: absolute;
    background-color: rgb(0, 0, 0);
    border-top-width: 5px;
    border-bottom-width: 5px;
    border-left-width: 5px;
    border-right-width: 5px;
    border-color: rgb(14, 14, 14);
    padding-top: 16px;
    padding-bottom: 16px;
    padding-left: 24px;
    padding-right: 24px;
    min-width: 250px;
    max-width: 350px;
    flex-direction: column;
}

.tooltip-name-field {
    margin-bottom: 10px;
    font-size: 16px;
    -unity-font-style-and-weight: bold;
    background-color: rgb(25, 25, 25);
    color: rgb(255, 255, 255);
    border-top-width: 1px;
    border-bottom-width: 1px;
    border-left-width: 1px;
    border-right-width: 1px;
    border-color: rgb(60, 60, 60);
    padding-top: 6px;
    padding-bottom: 6px;
    padding-left: 8px;
    padding-right: 8px;
}

.tooltip-name-field .unity-text-field__input {
    background-color: rgb(25, 25, 25);
    color: rgb(255, 255, 255);
    border-width: 0;
}

.tooltip-details {
    font-size: 13px;
    color: rgb(197, 197, 197);
    white-space: normal;
    margin-top: 4px;
    margin-bottom: 4px;
}

.tooltip-description {
    font-size: 11px;
    color: rgba(197, 197, 197, 0.7);
    white-space: normal;
    margin-top: 8px;
    padding-top: 8px;
    border-top-width: 1px;
    border-top-color: rgba(60, 60, 60, 0.5);
    font-style: italic;
}

/* 
 * CONTEXT MENU
 */
#ContextMenu {
    position: absolute;
    background-color: rgb(0, 0, 0);
    border-width: 5px;
    border-color: rgb(14, 14, 14);
    padding-top: 8px;
    padding-bottom: 8px;
    min-width: 200px;
    display: none;
    flex-direction: column;
}

#ContextMenu Button {
    -unity-text-align: middle-left;
    padding-left: 16px;
    padding-right: 16px;
    padding-top: 8px;
    padding-bottom: 8px;
    margin: 0;
    border-width: 0;
    background-color: rgba(0, 0, 0, 0);
    font-size: 13px;
    color: rgb(159, 159, 159);
}

#ContextMenu Button:hover {
    background-color: rgba(33, 33, 33, 0.5);
    color: rgb(255, 255, 255);
}

.context-menu-container {
    position: absolute;
    background-color: rgb(0, 0, 0);
    border-top-width: 5px;
    border-bottom-width: 5px;
    border-left-width: 5px;
    border-right-width: 5px;
    border-color: rgb(14, 14, 14);
    padding-top: 8px;
    padding-bottom: 8px;
    flex-direction: column;
    min-width: 200px;
}

.context-name-section {
    padding: 8px;
    border-bottom: 1px solid #3a3a3a;
    margin-bottom: 4px;
}

.context-name-field {
    font-size: 16px;
    -unity-font-style-and-weight: bold;
    background-color: rgb(25, 25, 25);
    color: rgb(255, 255, 255);
    border-top-width: 1px;
    border-bottom-width: 1px;
    border-left-width: 1px;
    border-right-width: 1px;
    border-color: rgb(60, 60, 60);
    padding-top: 6px;
    padding-bottom: 6px;
    padding-left: 8px;
    padding-right: 8px;
}

.context-name-field .unity-text-field__input {
    background-color: rgb(25, 25, 25);
    color: rgb(255, 255, 255);
    border-width: 0;
}

.context-message {
    font-size: 13px;
    color: rgb(100, 100, 100);
    padding-left: 16px;
    padding-right: 16px;
    padding-top: 8px;
    padding-bottom: 8px;
}

.context-action-button {
    background-color: #2a2a2a;
    border-width: 1px;
    border-color: #444444;
    color: #ffffff;
    margin-top: 6px;
    padding: 4px 8px;
    border-radius: 3px;
}

.context-action-button:hover {
    background-color: #3a3a3a;
}

.context-action-button:active {
    background-color: #1a1a1a;
}

.separator {
    height: 1px;
    background-color: rgb(60, 60, 60);
    margin-top: 6px;
    margin-bottom: 6px;
    margin-left: 8px;
    margin-right: 8px;
}

/* 
 * BATTERY SEGMENTS - Updated styles for proper segment separation
 */ 

/* Battery bar now acts as a container for segments */ 
.battery-bar { 
    flex-grow: 1; 
    height: 8px; /* Slightly taller for better visibility */ 
    margin-left: 16px; 
    flex-direction: row; 
    position: relative; 
    align-items: center; 
    justify-content: flex-start; 
    background-color: transparent; /* Ensure no background */
    border-width: 0; /* No border on container */
} 

/* Container for each segment - FIXED WIDTH INSTEAD OF FLEX-GROW */ 
.battery-segment-container { 
    width: 9%; /* Fixed width for each segment */
    flex-grow: 0; /* Don't grow to fill space */
    flex-shrink: 0; /* Don't shrink */
    height: 100%; 
    margin-right: 1%; /* Gap as percentage */
    position: relative; 
    overflow: hidden; 
    border-width: 0;
    border-color: transparent;
    background-color: rgb(20, 20, 20); /* Dark background for empty state */
}

/* Remove margin from last segment */
.battery-segment-container:last-child {
    margin-right: 0;
}

/* The actual fill inside each segment */ 
.battery-segment { 
    position: absolute; 
    left: 0; 
    top: 0; 
    height: 100%; 
    width: 0%; /* Width controlled by script */ 
    transition: width 0.1s ease-out; 
    border-width: 0; /* No border on the fill itself */
} 

/* Single color for all segment fills - less bright */ 
.battery-segment-fill { 
    background-color: rgb(120, 120, 120); /* Less bright gray for filled segments */
    border-radius: 1px; /* Slight rounding for smoother look */
} 

/* Notification specific styles */
.energy-notification { 
    position: fixed; /* Fixed positioning to show regardless of parent visibility */
    bottom: 60px; /* Position above the main energy container */ 
    left: 50%; 
    transform: translateX(-50%); /* Center horizontally */
    width: 400px; /* Fixed width for consistent appearance */
    padding: 8px 24px; 
    background-color: rgba(0, 0, 0, 0.95); 
    height: 40px; 
    justify-content: center; 
    border-top-width: 5px; 
    border-left-width: 5px;
    border-right-width: 5px;
    border-color: rgb(14, 14, 14); 
    z-index: 1000; /* High z-index to show above everything */ 
    display: none; /* Hidden by default */ 
} 

/* Notification battery indicator */ 
.notification-battery-indicator { 
    height: 20px; 
    flex-direction: row; 
    align-items: center; 
} 

/* Optional: If you want to show empty segment outlines, add this class */
.battery-segment-container.show-empty-outline { 
    border-width: 1px;
    border-color: rgba(60, 60, 60, 0.3); /* Very subtle gray outline */
    background-color: rgba(30, 30, 30, 0.2); /* Very subtle fill */
}

/* Blinking animation for empty segments */ 
.battery-segment-blink { 
    animation: blink-animation 0.5s ease-in-out infinite; 
} 

@keyframes blink-animation { 
    0% { 
        opacity: 1; 
        background-color: transparent;
    } 
    50% { 
        opacity: 0.3; 
        background-color: rgba(200, 50, 50, 0.3); /* Red tint when blinking */ 
    } 
    100% { 
        opacity: 1; 
        background-color: transparent;
    } 
} 

/* For notification segments */
.energy-notification .battery-segment-container { 
    width: 9%; /* Same as main battery */
    flex-grow: 0;
    flex-shrink: 0;
    height: 10px; 
    border-width: 1px;
    border-color: rgb(40, 40, 40); /* Subtle border */
    background-color: rgb(20, 20, 20); /* Dark background for empty state */
    margin-right: 1%;
}

/* Remove margin from last notification segment */
.energy-notification .battery-segment-container:last-child {
    margin-right: 0;
}

.energy-notification .battery-segment {
    border-width: 0; /* No border on the fill itself */
} 

/* Notification specific icon style */ 
.notification-battery-icon { 
    width: 24px; 
    height: 24px; 
    -unity-background-scale-mode: scale-to-fit; 
} 

/* 10 segment layout - adjust as needed */ 
.battery-bar, 
.notification-battery-bar { 
    /* Ensure segments don't wrap */ 
    flex-wrap: nowrap; 
} 

/* For different segment counts, you might want responsive sizing */ 
/* 5 segments */ 
.battery-bar.segments-5 .battery-segment-container, 
.notification-battery-bar.segments-5 .battery-segment-container { 
    min-width: 18%; 
} 

/* Alternative approach if you have different segment counts */
.battery-bar.segments-10 .battery-segment-container {
    width: 9%; /* 10 segments × 9% = 90%, leaving 10% for gaps */
    margin-right: 1%;
}

.battery-bar.segments-5 .battery-segment-container {
    width: 18%; /* 5 segments × 18% = 90%, leaving 10% for gaps */
    margin-right: 2%;
}

/* Remove old battery fill styles since we're using segments now */
.battery-fill--low,
.battery-fill--medium,
.battery-fill--high {
    display: none;
}

/* Preview styles for consumable hover */
.battery-segment-preview {
    background-color: rgba(180, 180, 180, 0.4) !important; /* Light gray preview for empty segments */
}

.battery-segment-preview-enhance {
    background-color: rgba(180, 180, 180, 0.2) !important; /* Subtle enhancement for partial segments */
}