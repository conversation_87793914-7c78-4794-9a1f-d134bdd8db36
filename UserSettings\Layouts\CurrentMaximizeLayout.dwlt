%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &1
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 12010, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Children:
  - {fileID: 3}
  m_Position:
    serializedVersion: 2
    x: 0
    y: 36
    width: 2406
    height: 1286
  m_MinSize: {x: 400, y: 100}
  m_MaxSize: {x: 32384, y: 16192}
  vertical: 1
  controlID: 43389
  draggingID: 0
--- !u!114 &2
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 12015, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_MinSize: {x: 50, y: 50}
  m_MaxSize: {x: 4000, y: 4000}
  m_TitleContent:
    m_Text: Game
    m_Image: {fileID: -6423792434712278376, guid: 0000000000000000d000000000000000, type: 0}
    m_Tooltip: 
    m_TextWithWhitespace: "Game\u200B"
  m_Pos:
    serializedVersion: 2
    x: 895
    y: 111
    width: 1690
    height: 999
  m_SerializedDataModeController:
    m_DataMode: 0
    m_PreferredDataMode: 0
    m_SupportedDataModes: 
    isAutomatic: 1
  m_ViewDataDictionary: {fileID: 0}
  m_OverlayCanvas:
    m_LastAppliedPresetName: Default
    m_SaveData: []
    m_ContainerData: []
    m_OverlaysVisible: 1
  m_SerializedViewNames: []
  m_SerializedViewValues: []
  m_PlayModeViewName: GameView
  m_ShowGizmos: 0
  m_TargetDisplay: 0
  m_ClearColor: {r: 0, g: 0, b: 0, a: 0}
  m_TargetSize: {x: 1690, y: 978}
  m_TextureFilterMode: 0
  m_TextureHideFlags: 61
  m_RenderIMGUI: 1
  m_EnterPlayModeBehavior: 0
  m_UseMipMap: 0
  m_VSyncEnabled: 0
  m_Gizmos: 0
  m_Stats: 0
  m_SelectedSizes: 00000000000000000000000000000000000000000000000000000000000000000000000000000000
  m_ZoomArea:
    m_HRangeLocked: 0
    m_VRangeLocked: 0
    hZoomLockedByDefault: 0
    vZoomLockedByDefault: 0
    m_HBaseRangeMin: -845
    m_HBaseRangeMax: 845
    m_VBaseRangeMin: -489
    m_VBaseRangeMax: 489
    m_HAllowExceedBaseRangeMin: 1
    m_HAllowExceedBaseRangeMax: 1
    m_VAllowExceedBaseRangeMin: 1
    m_VAllowExceedBaseRangeMax: 1
    m_ScaleWithWindow: 0
    m_HSlider: 0
    m_VSlider: 0
    m_IgnoreScrollWheelUntilClicked: 0
    m_EnableMouseInput: 0
    m_EnableSliderZoomHorizontal: 0
    m_EnableSliderZoomVertical: 0
    m_UniformScale: 1
    m_UpDirection: 1
    m_DrawArea:
      serializedVersion: 2
      x: 0
      y: 21
      width: 1690
      height: 978
    m_Scale: {x: 1, y: 1}
    m_Translation: {x: 845, y: 489}
    m_MarginLeft: 0
    m_MarginRight: 0
    m_MarginTop: 0
    m_MarginBottom: 0
    m_LastShownAreaInsideMargins:
      serializedVersion: 2
      x: -845
      y: -489
      width: 1690
      height: 978
    m_MinimalGUI: 1
  m_defaultScale: 1
  m_LastWindowPixelSize: {x: 1690, y: 999}
  m_ClearInEditMode: 1
  m_NoCameraWarning: 1
  m_LowResolutionForAspectRatios: 01000000000000000000
  m_XRRenderMode: 0
  m_RenderTexture: {fileID: 0}
  m_showToolbar: 1
--- !u!114 &3
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 1
  m_Script: {fileID: 12010, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Children:
  - {fileID: 4}
  - {fileID: 25}
  m_Position:
    serializedVersion: 2
    x: 0
    y: 0
    width: 2406
    height: 1286
  m_MinSize: {x: 400, y: 100}
  m_MaxSize: {x: 32384, y: 16192}
  vertical: 0
  controlID: 43390
  draggingID: 0
--- !u!114 &4
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 1
  m_Script: {fileID: 12010, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Children:
  - {fileID: 5}
  - {fileID: 17}
  m_Position:
    serializedVersion: 2
    x: 0
    y: 0
    width: 1954
    height: 1286
  m_MinSize: {x: 300, y: 100}
  m_MaxSize: {x: 24288, y: 16192}
  vertical: 1
  controlID: 43391
  draggingID: 0
--- !u!114 &5
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 1
  m_Script: {fileID: 12010, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Children:
  - {fileID: 6}
  - {fileID: 9}
  m_Position:
    serializedVersion: 2
    x: 0
    y: 0
    width: 1954
    height: 1025
  m_MinSize: {x: 200, y: 50}
  m_MaxSize: {x: 16192, y: 8096}
  vertical: 0
  controlID: 43392
  draggingID: 0
--- !u!114 &6
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 12006, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: SceneHierarchyWindow
  m_EditorClassIdentifier: 
  m_Children: []
  m_Position:
    serializedVersion: 2
    x: 0
    y: 0
    width: 262
    height: 1025
  m_MinSize: {x: 201, y: 226}
  m_MaxSize: {x: 4001, y: 4026}
  m_ActualView: {fileID: 8}
  m_Panes:
  - {fileID: 7}
  - {fileID: 8}
  m_Selected: 1
  m_LastSelected: 0
--- !u!114 &7
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 12070, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_MinSize: {x: 900, y: 216}
  m_MaxSize: {x: 4000, y: 4000}
  m_TitleContent:
    m_Text: Profiler
    m_Image: {fileID: -1089619856830078684, guid: 0000000000000000d000000000000000, type: 0}
    m_Tooltip: 
    m_TextWithWhitespace: "Profiler\u200B"
  m_Pos:
    serializedVersion: 2
    x: 575
    y: 110
    width: 261
    height: 999
  m_SerializedDataModeController:
    m_DataMode: 0
    m_PreferredDataMode: 0
    m_SupportedDataModes: 
    isAutomatic: 1
  m_ViewDataDictionary: {fileID: 0}
  m_OverlayCanvas:
    m_LastAppliedPresetName: Default
    m_SaveData: []
    m_ContainerData: []
    m_OverlaysVisible: 1
  m_Recording: 1
  m_ActiveNativePlatformSupportModuleName: 
  m_AllModules:
  - rid: 6377673351905411183
  - rid: 6377673351905411184
  - rid: 6377673351905411185
  - rid: 6377673351905411186
  - rid: 6377673351905411187
  - rid: 6377673351905411188
  - rid: 6377673351905411189
  - rid: 6377673351905411190
  - rid: 6377673351905411191
  - rid: 6377673351905411192
  - rid: 6377673351905411193
  - rid: 6377673351905411194
  - rid: 6377673351905411195
  - rid: 6377673351905411196
  m_CallstackRecordMode: 1
  m_ClearOnPlay: 0
  references:
    version: 2
    RefIds:
    - rid: 6377673351905411183
      type: {class: CPUProfilerModule, ns: UnityEditorInternal.Profiling, asm: UnityEditor.CoreModule}
      data:
        m_Identifier: UnityEditorInternal.Profiling.CPUProfilerModule, UnityEditor.CoreModule,
          Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
        m_PaneScroll: {x: 0, y: 0}
        m_ViewType: 3
        updateViewLive: 0
        m_CurrentFrameIndex: -1
        m_HierarchyOverruledThreadFromSelection: 0
        m_ProfilerViewFilteringOptions: 1
        m_FrameDataHierarchyView:
          m_Serialized: 1
          m_TreeViewState:
            scrollPos: {x: 0, y: 16}
            m_SelectedIDs: 87010000
            m_LastClickedID: 0
            m_ExpandedIDs: c700000086010000870100009401000099010000ba010000bb010000e60100003802000050020000730200009402000023030000240300002503000013070000
            m_RenameOverlay:
              m_UserAcceptedRename: 0
              m_Name: 
              m_OriginalName: 
              m_EditFieldRect:
                serializedVersion: 2
                x: 0
                y: 0
                width: 0
                height: 0
              m_UserData: 0
              m_IsWaitingForDelay: 0
              m_IsRenaming: 0
              m_OriginalEventType: 11
              m_IsRenamingFilename: 0
              m_TrimLeadingAndTrailingWhitespace: 0
              m_ClientGUIView: {fileID: 0}
            m_SearchString: 
          m_MultiColumnHeaderState:
            m_Columns:
            - width: 252
              sortedAscending: 1
              headerContent:
                m_Text: Overview
                m_Image: {fileID: 0}
                m_Tooltip: 
                m_TextWithWhitespace: "Overview\u200B"
              contextMenuText: 
              headerTextAlignment: 0
              sortingArrowAlignment: 2
              minWidth: 200
              maxWidth: 1000000
              autoResize: 1
              allowToggleVisibility: 0
              canSort: 1
              userData: 0
            - width: 80
              sortedAscending: 0
              headerContent:
                m_Text: Total
                m_Image: {fileID: 0}
                m_Tooltip: 
                m_TextWithWhitespace: "Total\u200B"
              contextMenuText: 
              headerTextAlignment: 0
              sortingArrowAlignment: 2
              minWidth: 50
              maxWidth: 1000000
              autoResize: 0
              allowToggleVisibility: 1
              canSort: 1
              userData: 0
            - width: 80
              sortedAscending: 0
              headerContent:
                m_Text: Self
                m_Image: {fileID: 0}
                m_Tooltip: 
                m_TextWithWhitespace: "Self\u200B"
              contextMenuText: 
              headerTextAlignment: 0
              sortingArrowAlignment: 2
              minWidth: 50
              maxWidth: 1000000
              autoResize: 0
              allowToggleVisibility: 1
              canSort: 1
              userData: 0
            - width: 80
              sortedAscending: 0
              headerContent:
                m_Text: Calls
                m_Image: {fileID: 0}
                m_Tooltip: 
                m_TextWithWhitespace: "Calls\u200B"
              contextMenuText: 
              headerTextAlignment: 0
              sortingArrowAlignment: 2
              minWidth: 50
              maxWidth: 1000000
              autoResize: 0
              allowToggleVisibility: 1
              canSort: 1
              userData: 0
            - width: 80
              sortedAscending: 0
              headerContent:
                m_Text: GC Alloc
                m_Image: {fileID: 0}
                m_Tooltip: 
                m_TextWithWhitespace: "GC Alloc\u200B"
              contextMenuText: 
              headerTextAlignment: 0
              sortingArrowAlignment: 2
              minWidth: 50
              maxWidth: 1000000
              autoResize: 0
              allowToggleVisibility: 1
              canSort: 1
              userData: 0
            - width: 80
              sortedAscending: 0
              headerContent:
                m_Text: Time ms
                m_Image: {fileID: 0}
                m_Tooltip: 
                m_TextWithWhitespace: "Time ms\u200B"
              contextMenuText: 
              headerTextAlignment: 0
              sortingArrowAlignment: 2
              minWidth: 50
              maxWidth: 1000000
              autoResize: 0
              allowToggleVisibility: 1
              canSort: 1
              userData: 0
            - width: 80
              sortedAscending: 0
              headerContent:
                m_Text: Self ms
                m_Image: {fileID: 0}
                m_Tooltip: 
                m_TextWithWhitespace: "Self ms\u200B"
              contextMenuText: 
              headerTextAlignment: 0
              sortingArrowAlignment: 2
              minWidth: 50
              maxWidth: 1000000
              autoResize: 0
              allowToggleVisibility: 1
              canSort: 1
              userData: 0
            - width: 25
              sortedAscending: 0
              headerContent:
                m_Text: 
                m_Image: {fileID: -5161429177145976760, guid: 0000000000000000d000000000000000, type: 0}
                m_Tooltip: Warnings
                m_TextWithWhitespace: 
              contextMenuText: 
              headerTextAlignment: 0
              sortingArrowAlignment: 2
              minWidth: 25
              maxWidth: 25
              autoResize: 0
              allowToggleVisibility: 1
              canSort: 1
              userData: 0
            m_VisibleColumns: 0000000001000000020000000300000004000000050000000600000007000000
            m_SortedColumns: 05000000
          m_ThreadIndexInThreadNames: 0
          m_DetailedViewType: 0
          m_DetailedViewSpliterState:
            ID: 0
            splitterInitialOffset: 0
            currentActiveSplitter: -1
            realSizes:
            - 0
            - 0
            relativeSizes:
            - 0.7
            - 0.3
            minSizes:
            - 450
            - 50
            maxSizes:
            - 0
            - 0
            lastTotalSize: 0
            splitSize: 6
            xOffset: 0
            m_Version: 1
            oldRealSizes: 
            oldMinSizes: 
            oldMaxSizes: 
            oldSplitSize: 0
          m_DetailedObjectsView:
            m_SelectedID: -1
            m_TreeViewState:
              scrollPos: {x: 0, y: 0}
              m_SelectedIDs: 
              m_LastClickedID: 0
              m_ExpandedIDs: 
              m_RenameOverlay:
                m_UserAcceptedRename: 0
                m_Name: 
                m_OriginalName: 
                m_EditFieldRect:
                  serializedVersion: 2
                  x: 0
                  y: 0
                  width: 0
                  height: 0
                m_UserData: 0
                m_IsWaitingForDelay: 0
                m_IsRenaming: 0
                m_OriginalEventType: 11
                m_IsRenamingFilename: 0
                m_TrimLeadingAndTrailingWhitespace: 0
                m_ClientGUIView: {fileID: 0}
              m_SearchString: 
            m_MultiColumnHeaderState:
              m_Columns: []
              m_VisibleColumns: 
              m_SortedColumns: 
            m_VertSplit:
              ID: 0
              splitterInitialOffset: 0
              currentActiveSplitter: 0
              realSizes: []
              relativeSizes: []
              minSizes: []
              maxSizes: []
              lastTotalSize: 0
              splitSize: 0
              xOffset: 0
              m_Version: 1
              oldRealSizes: 
              oldMinSizes: 
              oldMaxSizes: 
              oldSplitSize: 0
          m_DetailedCallsView:
            m_SelectedID: -1
            m_VertSplit:
              ID: 0
              splitterInitialOffset: 0
              currentActiveSplitter: 0
              realSizes: []
              relativeSizes: []
              minSizes: []
              maxSizes: []
              lastTotalSize: 0
              splitSize: 0
              xOffset: 0
              m_Version: 1
              oldRealSizes: 
              oldMinSizes: 
              oldMaxSizes: 
              oldSplitSize: 0
            m_CalleesTreeView:
              m_ViewState:
                scrollPos: {x: 0, y: 0}
                m_SelectedIDs: 
                m_LastClickedID: 0
                m_ExpandedIDs: 
                m_RenameOverlay:
                  m_UserAcceptedRename: 0
                  m_Name: 
                  m_OriginalName: 
                  m_EditFieldRect:
                    serializedVersion: 2
                    x: 0
                    y: 0
                    width: 0
                    height: 0
                  m_UserData: 0
                  m_IsWaitingForDelay: 0
                  m_IsRenaming: 0
                  m_OriginalEventType: 11
                  m_IsRenamingFilename: 0
                  m_TrimLeadingAndTrailingWhitespace: 0
                  m_ClientGUIView: {fileID: 0}
                m_SearchString: 
              m_ViewHeaderState:
                m_Columns:
                - width: 150
                  sortedAscending: 1
                  headerContent:
                    m_Text: Called From
                    m_Image: {fileID: 0}
                    m_Tooltip: 'Parents the selected function is called from


                      (Press
                      ''F'' for frame selection)'
                    m_TextWithWhitespace: "Called From\u200B"
                  contextMenuText: 
                  headerTextAlignment: 0
                  sortingArrowAlignment: 2
                  minWidth: 150
                  maxWidth: 1000000
                  autoResize: 1
                  allowToggleVisibility: 0
                  canSort: 1
                  userData: 0
                - width: 60
                  sortedAscending: 0
                  headerContent:
                    m_Text: Calls
                    m_Image: {fileID: 0}
                    m_Tooltip: Total number of calls in a selected frame
                    m_TextWithWhitespace: "Calls\u200B"
                  contextMenuText: 
                  headerTextAlignment: 0
                  sortingArrowAlignment: 2
                  minWidth: 60
                  maxWidth: 1000000
                  autoResize: 0
                  allowToggleVisibility: 1
                  canSort: 1
                  userData: 0
                - width: 60
                  sortedAscending: 0
                  headerContent:
                    m_Text: GC Alloc
                    m_Image: {fileID: 0}
                    m_Tooltip: 
                    m_TextWithWhitespace: "GC Alloc\u200B"
                  contextMenuText: 
                  headerTextAlignment: 0
                  sortingArrowAlignment: 2
                  minWidth: 60
                  maxWidth: 1000000
                  autoResize: 0
                  allowToggleVisibility: 1
                  canSort: 1
                  userData: 0
                - width: 60
                  sortedAscending: 0
                  headerContent:
                    m_Text: Time ms
                    m_Image: {fileID: 0}
                    m_Tooltip: Total time the selected function spends within a parent
                    m_TextWithWhitespace: "Time ms\u200B"
                  contextMenuText: 
                  headerTextAlignment: 0
                  sortingArrowAlignment: 2
                  minWidth: 60
                  maxWidth: 1000000
                  autoResize: 0
                  allowToggleVisibility: 1
                  canSort: 1
                  userData: 0
                - width: 60
                  sortedAscending: 0
                  headerContent:
                    m_Text: Time %
                    m_Image: {fileID: 0}
                    m_Tooltip: Shows how often the selected function was called from
                      the parent call
                    m_TextWithWhitespace: "Time %\u200B"
                  contextMenuText: 
                  headerTextAlignment: 0
                  sortingArrowAlignment: 2
                  minWidth: 60
                  maxWidth: 1000000
                  autoResize: 0
                  allowToggleVisibility: 1
                  canSort: 1
                  userData: 0
                m_VisibleColumns: 0000000001000000020000000300000004000000
                m_SortedColumns: 03000000
            m_CallersTreeView:
              m_ViewState:
                scrollPos: {x: 0, y: 0}
                m_SelectedIDs: 
                m_LastClickedID: 0
                m_ExpandedIDs: 
                m_RenameOverlay:
                  m_UserAcceptedRename: 0
                  m_Name: 
                  m_OriginalName: 
                  m_EditFieldRect:
                    serializedVersion: 2
                    x: 0
                    y: 0
                    width: 0
                    height: 0
                  m_UserData: 0
                  m_IsWaitingForDelay: 0
                  m_IsRenaming: 0
                  m_OriginalEventType: 11
                  m_IsRenamingFilename: 0
                  m_TrimLeadingAndTrailingWhitespace: 0
                  m_ClientGUIView: {fileID: 0}
                m_SearchString: 
              m_ViewHeaderState:
                m_Columns:
                - width: 150
                  sortedAscending: 1
                  headerContent:
                    m_Text: Called From
                    m_Image: {fileID: 0}
                    m_Tooltip: 'Parents the selected function is called from


                      (Press
                      ''F'' for frame selection)'
                    m_TextWithWhitespace: "Called From\u200B"
                  contextMenuText: 
                  headerTextAlignment: 0
                  sortingArrowAlignment: 2
                  minWidth: 150
                  maxWidth: 1000000
                  autoResize: 1
                  allowToggleVisibility: 0
                  canSort: 1
                  userData: 0
                - width: 60
                  sortedAscending: 0
                  headerContent:
                    m_Text: Calls
                    m_Image: {fileID: 0}
                    m_Tooltip: Total number of calls in a selected frame
                    m_TextWithWhitespace: "Calls\u200B"
                  contextMenuText: 
                  headerTextAlignment: 0
                  sortingArrowAlignment: 2
                  minWidth: 60
                  maxWidth: 1000000
                  autoResize: 0
                  allowToggleVisibility: 1
                  canSort: 1
                  userData: 0
                - width: 60
                  sortedAscending: 0
                  headerContent:
                    m_Text: GC Alloc
                    m_Image: {fileID: 0}
                    m_Tooltip: 
                    m_TextWithWhitespace: "GC Alloc\u200B"
                  contextMenuText: 
                  headerTextAlignment: 0
                  sortingArrowAlignment: 2
                  minWidth: 60
                  maxWidth: 1000000
                  autoResize: 0
                  allowToggleVisibility: 1
                  canSort: 1
                  userData: 0
                - width: 60
                  sortedAscending: 0
                  headerContent:
                    m_Text: Time ms
                    m_Image: {fileID: 0}
                    m_Tooltip: Total time the selected function spends within a parent
                    m_TextWithWhitespace: "Time ms\u200B"
                  contextMenuText: 
                  headerTextAlignment: 0
                  sortingArrowAlignment: 2
                  minWidth: 60
                  maxWidth: 1000000
                  autoResize: 0
                  allowToggleVisibility: 1
                  canSort: 1
                  userData: 0
                - width: 60
                  sortedAscending: 0
                  headerContent:
                    m_Text: Time %
                    m_Image: {fileID: 0}
                    m_Tooltip: Shows how often the selected function was called from
                      the parent call
                    m_TextWithWhitespace: "Time %\u200B"
                  contextMenuText: 
                  headerTextAlignment: 0
                  sortingArrowAlignment: 2
                  minWidth: 60
                  maxWidth: 1000000
                  autoResize: 0
                  allowToggleVisibility: 1
                  canSort: 1
                  userData: 0
                m_VisibleColumns: 0000000001000000020000000300000004000000
                m_SortedColumns: 03000000
          m_FullThreadName: Main Thread
          m_ThreadName: Main Thread
          <threadId>k__BackingField: 20732
          <threadIndex>k__BackingField: 0
          m_GroupName: 
    - rid: 6377673351905411184
      type: {class: GPUProfilerModule, ns: UnityEditorInternal.Profiling, asm: UnityEditor.CoreModule}
      data:
        m_Identifier: UnityEditorInternal.Profiling.GPUProfilerModule, UnityEditor.CoreModule,
          Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
        m_PaneScroll: {x: 0, y: 0}
        m_ViewType: 0
        updateViewLive: 0
        m_CurrentFrameIndex: -1
        m_HierarchyOverruledThreadFromSelection: 0
        m_ProfilerViewFilteringOptions: 1
        m_FrameDataHierarchyView:
          m_Serialized: 0
          m_TreeViewState:
            scrollPos: {x: 0, y: 0}
            m_SelectedIDs: 
            m_LastClickedID: 0
            m_ExpandedIDs: 
            m_RenameOverlay:
              m_UserAcceptedRename: 0
              m_Name: 
              m_OriginalName: 
              m_EditFieldRect:
                serializedVersion: 2
                x: 0
                y: 0
                width: 0
                height: 0
              m_UserData: 0
              m_IsWaitingForDelay: 0
              m_IsRenaming: 0
              m_OriginalEventType: 11
              m_IsRenamingFilename: 0
              m_TrimLeadingAndTrailingWhitespace: 0
              m_ClientGUIView: {fileID: 0}
            m_SearchString: 
          m_MultiColumnHeaderState:
            m_Columns: []
            m_VisibleColumns: 
            m_SortedColumns: 
          m_ThreadIndexInThreadNames: 0
          m_DetailedViewType: 0
          m_DetailedViewSpliterState:
            ID: 0
            splitterInitialOffset: 0
            currentActiveSplitter: 0
            realSizes: []
            relativeSizes: []
            minSizes: []
            maxSizes: []
            lastTotalSize: 0
            splitSize: 0
            xOffset: 0
            m_Version: 1
            oldRealSizes: 
            oldMinSizes: 
            oldMaxSizes: 
            oldSplitSize: 0
          m_DetailedObjectsView:
            m_SelectedID: 0
            m_TreeViewState:
              scrollPos: {x: 0, y: 0}
              m_SelectedIDs: 
              m_LastClickedID: 0
              m_ExpandedIDs: 
              m_RenameOverlay:
                m_UserAcceptedRename: 0
                m_Name: 
                m_OriginalName: 
                m_EditFieldRect:
                  serializedVersion: 2
                  x: 0
                  y: 0
                  width: 0
                  height: 0
                m_UserData: 0
                m_IsWaitingForDelay: 0
                m_IsRenaming: 0
                m_OriginalEventType: 11
                m_IsRenamingFilename: 0
                m_TrimLeadingAndTrailingWhitespace: 0
                m_ClientGUIView: {fileID: 0}
              m_SearchString: 
            m_MultiColumnHeaderState:
              m_Columns: []
              m_VisibleColumns: 
              m_SortedColumns: 
            m_VertSplit:
              ID: 0
              splitterInitialOffset: 0
              currentActiveSplitter: 0
              realSizes: []
              relativeSizes: []
              minSizes: []
              maxSizes: []
              lastTotalSize: 0
              splitSize: 0
              xOffset: 0
              m_Version: 1
              oldRealSizes: 
              oldMinSizes: 
              oldMaxSizes: 
              oldSplitSize: 0
          m_DetailedCallsView:
            m_SelectedID: -1
            m_VertSplit:
              ID: 0
              splitterInitialOffset: 0
              currentActiveSplitter: 0
              realSizes: []
              relativeSizes: []
              minSizes: []
              maxSizes: []
              lastTotalSize: 0
              splitSize: 0
              xOffset: 0
              m_Version: 1
              oldRealSizes: 
              oldMinSizes: 
              oldMaxSizes: 
              oldSplitSize: 0
            m_CalleesTreeView:
              m_ViewState:
                scrollPos: {x: 0, y: 0}
                m_SelectedIDs: 
                m_LastClickedID: 0
                m_ExpandedIDs: 
                m_RenameOverlay:
                  m_UserAcceptedRename: 0
                  m_Name: 
                  m_OriginalName: 
                  m_EditFieldRect:
                    serializedVersion: 2
                    x: 0
                    y: 0
                    width: 0
                    height: 0
                  m_UserData: 0
                  m_IsWaitingForDelay: 0
                  m_IsRenaming: 0
                  m_OriginalEventType: 11
                  m_IsRenamingFilename: 0
                  m_TrimLeadingAndTrailingWhitespace: 0
                  m_ClientGUIView: {fileID: 0}
                m_SearchString: 
              m_ViewHeaderState:
                m_Columns:
                - width: 150
                  sortedAscending: 1
                  headerContent:
                    m_Text: Called From
                    m_Image: {fileID: 0}
                    m_Tooltip: 'Parents the selected function is called from


                      (Press
                      ''F'' for frame selection)'
                    m_TextWithWhitespace: "Called From\u200B"
                  contextMenuText: 
                  headerTextAlignment: 0
                  sortingArrowAlignment: 2
                  minWidth: 150
                  maxWidth: 1000000
                  autoResize: 1
                  allowToggleVisibility: 0
                  canSort: 1
                  userData: 0
                - width: 60
                  sortedAscending: 0
                  headerContent:
                    m_Text: Calls
                    m_Image: {fileID: 0}
                    m_Tooltip: Total number of calls in a selected frame
                    m_TextWithWhitespace: "Calls\u200B"
                  contextMenuText: 
                  headerTextAlignment: 0
                  sortingArrowAlignment: 2
                  minWidth: 60
                  maxWidth: 1000000
                  autoResize: 0
                  allowToggleVisibility: 1
                  canSort: 1
                  userData: 0
                - width: 60
                  sortedAscending: 0
                  headerContent:
                    m_Text: GC Alloc
                    m_Image: {fileID: 0}
                    m_Tooltip: 
                    m_TextWithWhitespace: "GC Alloc\u200B"
                  contextMenuText: 
                  headerTextAlignment: 0
                  sortingArrowAlignment: 2
                  minWidth: 60
                  maxWidth: 1000000
                  autoResize: 0
                  allowToggleVisibility: 1
                  canSort: 1
                  userData: 0
                - width: 60
                  sortedAscending: 0
                  headerContent:
                    m_Text: Time ms
                    m_Image: {fileID: 0}
                    m_Tooltip: Total time the selected function spends within a parent
                    m_TextWithWhitespace: "Time ms\u200B"
                  contextMenuText: 
                  headerTextAlignment: 0
                  sortingArrowAlignment: 2
                  minWidth: 60
                  maxWidth: 1000000
                  autoResize: 0
                  allowToggleVisibility: 1
                  canSort: 1
                  userData: 0
                - width: 60
                  sortedAscending: 0
                  headerContent:
                    m_Text: Time %
                    m_Image: {fileID: 0}
                    m_Tooltip: Shows how often the selected function was called from
                      the parent call
                    m_TextWithWhitespace: "Time %\u200B"
                  contextMenuText: 
                  headerTextAlignment: 0
                  sortingArrowAlignment: 2
                  minWidth: 60
                  maxWidth: 1000000
                  autoResize: 0
                  allowToggleVisibility: 1
                  canSort: 1
                  userData: 0
                m_VisibleColumns: 0000000001000000020000000300000004000000
                m_SortedColumns: 03000000
            m_CallersTreeView:
              m_ViewState:
                scrollPos: {x: 0, y: 0}
                m_SelectedIDs: 
                m_LastClickedID: 0
                m_ExpandedIDs: 
                m_RenameOverlay:
                  m_UserAcceptedRename: 0
                  m_Name: 
                  m_OriginalName: 
                  m_EditFieldRect:
                    serializedVersion: 2
                    x: 0
                    y: 0
                    width: 0
                    height: 0
                  m_UserData: 0
                  m_IsWaitingForDelay: 0
                  m_IsRenaming: 0
                  m_OriginalEventType: 11
                  m_IsRenamingFilename: 0
                  m_TrimLeadingAndTrailingWhitespace: 0
                  m_ClientGUIView: {fileID: 0}
                m_SearchString: 
              m_ViewHeaderState:
                m_Columns:
                - width: 150
                  sortedAscending: 1
                  headerContent:
                    m_Text: Called From
                    m_Image: {fileID: 0}
                    m_Tooltip: 'Parents the selected function is called from


                      (Press
                      ''F'' for frame selection)'
                    m_TextWithWhitespace: "Called From\u200B"
                  contextMenuText: 
                  headerTextAlignment: 0
                  sortingArrowAlignment: 2
                  minWidth: 150
                  maxWidth: 1000000
                  autoResize: 1
                  allowToggleVisibility: 0
                  canSort: 1
                  userData: 0
                - width: 60
                  sortedAscending: 0
                  headerContent:
                    m_Text: Calls
                    m_Image: {fileID: 0}
                    m_Tooltip: Total number of calls in a selected frame
                    m_TextWithWhitespace: "Calls\u200B"
                  contextMenuText: 
                  headerTextAlignment: 0
                  sortingArrowAlignment: 2
                  minWidth: 60
                  maxWidth: 1000000
                  autoResize: 0
                  allowToggleVisibility: 1
                  canSort: 1
                  userData: 0
                - width: 60
                  sortedAscending: 0
                  headerContent:
                    m_Text: GC Alloc
                    m_Image: {fileID: 0}
                    m_Tooltip: 
                    m_TextWithWhitespace: "GC Alloc\u200B"
                  contextMenuText: 
                  headerTextAlignment: 0
                  sortingArrowAlignment: 2
                  minWidth: 60
                  maxWidth: 1000000
                  autoResize: 0
                  allowToggleVisibility: 1
                  canSort: 1
                  userData: 0
                - width: 60
                  sortedAscending: 0
                  headerContent:
                    m_Text: Time ms
                    m_Image: {fileID: 0}
                    m_Tooltip: Total time the selected function spends within a parent
                    m_TextWithWhitespace: "Time ms\u200B"
                  contextMenuText: 
                  headerTextAlignment: 0
                  sortingArrowAlignment: 2
                  minWidth: 60
                  maxWidth: 1000000
                  autoResize: 0
                  allowToggleVisibility: 1
                  canSort: 1
                  userData: 0
                - width: 60
                  sortedAscending: 0
                  headerContent:
                    m_Text: Time %
                    m_Image: {fileID: 0}
                    m_Tooltip: Shows how often the selected function was called from
                      the parent call
                    m_TextWithWhitespace: "Time %\u200B"
                  contextMenuText: 
                  headerTextAlignment: 0
                  sortingArrowAlignment: 2
                  minWidth: 60
                  maxWidth: 1000000
                  autoResize: 0
                  allowToggleVisibility: 1
                  canSort: 1
                  userData: 0
                m_VisibleColumns: 0000000001000000020000000300000004000000
                m_SortedColumns: 03000000
          m_FullThreadName: Main Thread
          m_ThreadName: Main Thread
          <threadId>k__BackingField: 0
          <threadIndex>k__BackingField: -1
          m_GroupName: 
    - rid: 6377673351905411185
      type: {class: RenderingProfilerModule, ns: UnityEditorInternal.Profiling, asm: UnityEditor.CoreModule}
      data:
        m_Identifier: UnityEditorInternal.Profiling.RenderingProfilerModule, UnityEditor.CoreModule,
          Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
        m_PaneScroll: {x: 0, y: 0}
    - rid: 6377673351905411186
      type: {class: MemoryProfilerModule, ns: UnityEditorInternal.Profiling, asm: UnityEditor.CoreModule}
      data:
        m_Identifier: UnityEditorInternal.Profiling.MemoryProfilerModule, UnityEditor.CoreModule,
          Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
        m_PaneScroll: {x: 0, y: 0}
        m_ViewSplit:
          ID: 0
          splitterInitialOffset: 0
          currentActiveSplitter: -1
          realSizes:
          - 0
          - 0
          relativeSizes:
          - 0.7
          - 0.3
          minSizes:
          - 450
          - 50
          maxSizes:
          - 0
          - 0
          lastTotalSize: 0
          splitSize: 6
          xOffset: 0
          m_Version: 1
          oldRealSizes: 
          oldMinSizes: 
          oldMaxSizes: 
          oldSplitSize: 0
    - rid: 6377673351905411187
      type: {class: AudioProfilerModule, ns: UnityEditorInternal.Profiling, asm: UnityEditor.CoreModule}
      data:
        m_Identifier: UnityEditorInternal.Profiling.AudioProfilerModule, UnityEditor.CoreModule,
          Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
        m_PaneScroll: {x: 0, y: 0}
        m_ShowInactiveDSPChains: 0
        m_HighlightAudibleDSPChains: 1
        m_DSPGraphZoomFactor: 1
        m_DSPGraphHorizontalLayout: 0
    - rid: 6377673351905411188
      type: {class: VideoProfilerModule, ns: UnityEditorInternal.Profiling, asm: UnityEditor.CoreModule}
      data:
        m_Identifier: UnityEditorInternal.Profiling.VideoProfilerModule, UnityEditor.CoreModule,
          Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
        m_PaneScroll: {x: 0, y: 0}
    - rid: 6377673351905411189
      type: {class: PhysicsProfilerModule, ns: UnityEditorInternal.Profiling, asm: UnityEditor.CoreModule}
      data:
        m_Identifier: UnityEditorInternal.Profiling.PhysicsProfilerModule, UnityEditor.CoreModule,
          Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
        m_PaneScroll: {x: 0, y: 0}
    - rid: 6377673351905411190
      type: {class: Physics2DProfilerModule, ns: UnityEditorInternal.Profiling, asm: UnityEditor.CoreModule}
      data:
        m_Identifier: UnityEditorInternal.Profiling.Physics2DProfilerModule, UnityEditor.CoreModule,
          Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
        m_PaneScroll: {x: 0, y: 0}
    - rid: 6377673351905411191
      type: {class: UIProfilerModule, ns: UnityEditorInternal.Profiling, asm: UnityEditor.CoreModule}
      data:
        m_Identifier: UnityEditorInternal.Profiling.UIProfilerModule, UnityEditor.CoreModule,
          Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
        m_PaneScroll: {x: 0, y: 0}
    - rid: 6377673351905411192
      type: {class: UIDetailsProfilerModule, ns: UnityEditorInternal.Profiling, asm: UnityEditor.CoreModule}
      data:
        m_Identifier: UnityEditorInternal.Profiling.UIDetailsProfilerModule, UnityEditor.CoreModule,
          Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
        m_PaneScroll: {x: 0, y: 0}
    - rid: 6377673351905411193
      type: {class: GlobalIlluminationProfilerModule, ns: UnityEditorInternal.Profiling, asm: UnityEditor.CoreModule}
      data:
        m_Identifier: UnityEditorInternal.Profiling.GlobalIlluminationProfilerModule,
          UnityEditor.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
        m_PaneScroll: {x: 0, y: 0}
    - rid: 6377673351905411194
      type: {class: VirtualTexturingProfilerModule, ns: UnityEditorInternal.Profiling, asm: UnityEditor.CoreModule}
      data:
        m_Identifier: UnityEditorInternal.Profiling.VirtualTexturingProfilerModule,
          UnityEditor.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
        m_PaneScroll: {x: 0, y: 0}
        m_VTProfilerView:
          rid: 6377673351905411197
    - rid: 6377673351905411195
      type: {class: FileIOProfilerModule, ns: UnityEditorInternal.Profiling, asm: UnityEditor.CoreModule}
      data:
        m_Identifier: UnityEditorInternal.Profiling.FileIOProfilerModule, UnityEditor.CoreModule,
          Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
        m_PaneScroll: {x: 0, y: 0}
    - rid: 6377673351905411196
      type: {class: AssetLoadingProfilerModule, ns: UnityEditorInternal.Profiling, asm: UnityEditor.CoreModule}
      data:
        m_Identifier: UnityEditorInternal.Profiling.AssetLoadingProfilerModule, UnityEditor.CoreModule,
          Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
        m_PaneScroll: {x: 0, y: 0}
    - rid: 6377673351905411197
      type: {class: VirtualTexturingProfilerView, ns: UnityEditor, asm: UnityEditor.CoreModule}
      data:
        m_SortAscending: 0
        m_SortedColumn: -1
--- !u!114 &8
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 1
  m_Script: {fileID: 12061, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_MinSize: {x: 200, y: 200}
  m_MaxSize: {x: 4000, y: 4000}
  m_TitleContent:
    m_Text: Hierarchy
    m_Image: {fileID: 7966133145522015247, guid: 0000000000000000d000000000000000, type: 0}
    m_Tooltip: 
    m_TextWithWhitespace: "Hierarchy\u200B"
  m_Pos:
    serializedVersion: 2
    x: 633
    y: 111
    width: 261
    height: 999
  m_SerializedDataModeController:
    m_DataMode: 0
    m_PreferredDataMode: 0
    m_SupportedDataModes: 
    isAutomatic: 1
  m_ViewDataDictionary: {fileID: 0}
  m_OverlayCanvas:
    m_LastAppliedPresetName: Default
    m_SaveData: []
    m_ContainerData: []
    m_OverlaysVisible: 1
  m_SceneHierarchy:
    m_TreeViewState:
      scrollPos: {x: 0, y: 0}
      m_SelectedIDs: deb80000
      m_LastClickedID: 0
      m_ExpandedIDs: 0c4df2ff244df2ffdc52f2ffde58f2ff0259f2ff6c67f2ff8467f2ff666df2ff6c73f2ff9473f2fff28bf2ff0a8cf2ffe891f2ffea97f2ff0e98f2ffd6b4f2ffeeb4f2ffc8baf2ffc6c0f2ffe6c0f2ffee18f3ff0619f3ffe01ef3ffde24f3fffe24f3ffc04bf3ffd84bf3ff9051f3ff9257f3ffb657f3ffe679f3fffe79f3ffdc7ff3ffde85f3ff0286f3ffd6acf3ffeeacf3ffccb2f3ffceb8f3fff2b8f3ffd6cbf3ffeecbf3ffccd1f3ffced7f3fff2d7f3ff56ebf3ff6eebf3ff4cf1f3ff4ef7f3ff72f7f3ffa610f4ffbe10f4ff9c16f4ff9e1cf4ffc21cf4ff6c2ff4ff842ff4ff5e35f4ff5c3bf4ff7c3bf4ffd862f4fff062f4ffa868f4ffaa6ef4ffce6ef4ff987df4ffb07df4ff8e83f4ff9089f4ffb489f4ff529af4ff74f2f4ff20f8f4fffe04f5ff1605f5ffce0af5ffd010f5fff410f5ff7028f5ff8828f5ff622ef5ff6034f5ff8034f5ff0a87f5ff2287f5ffda8cf5ffdc92f5ff0093f5ff62a4f5ff7aa4f5ff54aaf5ff52b0f5ff72b0f5ffcc47f6ffe447f6ffc24df6ffc453f6ffe853f6ffde5cf9fff65cf9ffd462f9ffd068f9fff468f9ffecfafffff4ffffffdad50000ded50000e2d5000014da000000de00001adf000000e000000ae00000e6e3000088e5000040e9000000eb0000e8eb000034ed00007ceb0c0082eb0c00e2eb0c0080f20c00fcff0c0082050d007eb91300785515001c881600
      m_RenameOverlay:
        m_UserAcceptedRename: 0
        m_Name: 
        m_OriginalName: 
        m_EditFieldRect:
          serializedVersion: 2
          x: 0
          y: 0
          width: 0
          height: 0
        m_UserData: 0
        m_IsWaitingForDelay: 0
        m_IsRenaming: 0
        m_OriginalEventType: 11
        m_IsRenamingFilename: 0
        m_TrimLeadingAndTrailingWhitespace: 0
        m_ClientGUIView: {fileID: 6}
      m_SearchString: 
    m_ExpandedScenes: []
    m_CurrenRootInstanceID: 0
    m_LockTracker:
      m_IsLocked: 0
    m_CurrentSortingName: TransformSorting
  m_WindowGUID: 4c969a2b90040154d917609493e03593
--- !u!114 &9
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 1
  m_Script: {fileID: 12006, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: GameView
  m_EditorClassIdentifier: 
  m_Children: []
  m_Position:
    serializedVersion: 2
    x: 262
    y: 0
    width: 1692
    height: 1025
  m_MinSize: {x: 52, y: 76}
  m_MaxSize: {x: 4002, y: 4026}
  m_ActualView: {fileID: 2}
  m_Panes:
  - {fileID: 10}
  - {fileID: 11}
  - {fileID: 12}
  - {fileID: 13}
  - {fileID: 14}
  - {fileID: 2}
  - {fileID: 15}
  - {fileID: 16}
  m_Selected: 5
  m_LastSelected: 4
--- !u!114 &10
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 13855, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_MinSize: {x: 310, y: 200}
  m_MaxSize: {x: 4000, y: 4000}
  m_TitleContent:
    m_Text: Preferences
    m_Image: {fileID: -5712115415447495865, guid: 0000000000000000d000000000000000, type: 0}
    m_Tooltip: 
    m_TextWithWhitespace: "Preferences\u200B"
  m_Pos:
    serializedVersion: 2
    x: 890
    y: 196
    width: 1726
    height: 919
  m_SerializedDataModeController:
    m_DataMode: 0
    m_PreferredDataMode: 0
    m_SupportedDataModes: 
    isAutomatic: 1
  m_ViewDataDictionary: {fileID: 0}
  m_OverlayCanvas:
    m_LastAppliedPresetName: Default
    m_SaveData: []
    m_ContainerData: []
    m_OverlaysVisible: 1
  m_PosLeft: {x: 0, y: 0}
  m_PosRight: {x: 0, y: 0}
  m_Scope: 0
  m_SplitterPos: 100
  m_SearchText: 
  m_TreeViewState:
    scrollPos: {x: 0, y: 0}
    m_SelectedIDs: ce3102cc
    m_LastClickedID: -872271410
    m_ExpandedIDs: 2956c29689577ec10000000001fecd11
    m_RenameOverlay:
      m_UserAcceptedRename: 0
      m_Name: 
      m_OriginalName: 
      m_EditFieldRect:
        serializedVersion: 2
        x: 0
        y: 0
        width: 0
        height: 0
      m_UserData: 0
      m_IsWaitingForDelay: 0
      m_IsRenaming: 0
      m_OriginalEventType: 11
      m_IsRenamingFilename: 0
      m_TrimLeadingAndTrailingWhitespace: 0
      m_ClientGUIView: {fileID: 0}
    m_SearchString: 
--- !u!114 &11
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 13854, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_MinSize: {x: 310, y: 200}
  m_MaxSize: {x: 4000, y: 4000}
  m_TitleContent:
    m_Text: Project Settings
    m_Image: {fileID: -5712115415447495865, guid: 0000000000000000d000000000000000, type: 0}
    m_Tooltip: 
    m_TextWithWhitespace: "Project Settings\u200B"
  m_Pos:
    serializedVersion: 2
    x: 942
    y: 108
    width: 1726
    height: 971
  m_SerializedDataModeController:
    m_DataMode: 0
    m_PreferredDataMode: 0
    m_SupportedDataModes: 
    isAutomatic: 1
  m_ViewDataDictionary: {fileID: 0}
  m_OverlayCanvas:
    m_LastAppliedPresetName: Default
    m_SaveData: []
    m_ContainerData: []
    m_OverlaysVisible: 1
  m_PosLeft: {x: 0, y: 0}
  m_PosRight: {x: 0, y: 0}
  m_Scope: 1
  m_SplitterPos: 168
  m_SearchText: 
  m_TreeViewState:
    scrollPos: {x: 0, y: 0}
    m_SelectedIDs: 029a15be
    m_LastClickedID: -1105880574
    m_ExpandedIDs: d7dddea267ea4eb100000000e594f01ac53aba5a
    m_RenameOverlay:
      m_UserAcceptedRename: 0
      m_Name: 
      m_OriginalName: 
      m_EditFieldRect:
        serializedVersion: 2
        x: 0
        y: 0
        width: 0
        height: 0
      m_UserData: 0
      m_IsWaitingForDelay: 0
      m_IsRenaming: 0
      m_OriginalEventType: 11
      m_IsRenamingFilename: 0
      m_TrimLeadingAndTrailingWhitespace: 0
      m_ClientGUIView: {fileID: 0}
    m_SearchString: 
--- !u!114 &12
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 12373, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_MinSize: {x: 50, y: 50}
  m_MaxSize: {x: 4000, y: 4000}
  m_TitleContent:
    m_Text: Audio Mixer
    m_Image: {fileID: 2344599766593239149, guid: 0000000000000000d000000000000000, type: 0}
    m_Tooltip: 
    m_TextWithWhitespace: "Audio Mixer\u200B"
  m_Pos:
    serializedVersion: 2
    x: 890
    y: 131
    width: 1726
    height: 971
  m_SerializedDataModeController:
    m_DataMode: 0
    m_PreferredDataMode: 0
    m_SupportedDataModes: 
    isAutomatic: 1
  m_ViewDataDictionary: {fileID: 0}
  m_OverlayCanvas:
    m_LastAppliedPresetName: Default
    m_SaveData: []
    m_ContainerData: []
    m_OverlaysVisible: 1
  m_MixersTreeState:
    scrollPos: {x: 0, y: 0}
    m_SelectedIDs: 
    m_LastClickedID: 52664
    m_ExpandedIDs: 12eb343c
    m_RenameOverlay:
      m_UserAcceptedRename: 0
      m_Name: 
      m_OriginalName: 
      m_EditFieldRect:
        serializedVersion: 2
        x: 0
        y: 0
        width: 0
        height: 0
      m_UserData: 0
      m_IsWaitingForDelay: 0
      m_IsRenaming: 0
      m_OriginalEventType: 11
      m_IsRenamingFilename: 0
      m_TrimLeadingAndTrailingWhitespace: 0
      m_ClientGUIView: {fileID: 9}
    m_SearchString: 
    m_CreateAssetUtility:
      m_EndAction: {fileID: 0}
      m_InstanceID: 0
      m_Path: 
      m_Icon: {fileID: 0}
      m_ResourceFile: 
  m_LayoutStripsOnTop:
    m_VerticalSplitter:
      ID: 0
      splitterInitialOffset: 0
      currentActiveSplitter: -1
      realSizes:
      - 65
      - 35
      relativeSizes:
      - 0.65
      - 0.35000002
      minSizes:
      - 85
      - 105
      maxSizes:
      - 0
      - 0
      lastTotalSize: 0
      splitSize: 6
      xOffset: 0
      m_Version: 1
      oldRealSizes: 
      oldMinSizes: 
      oldMaxSizes: 
      oldSplitSize: 0
    m_HorizontalSplitter:
      ID: 0
      splitterInitialOffset: 0
      currentActiveSplitter: -1
      realSizes:
      - 60
      - 60
      - 60
      - 60
      relativeSizes:
      - 0.25
      - 0.25
      - 0.25
      - 0.25
      minSizes:
      - 85
      - 85
      - 85
      - 85
      maxSizes:
      - 0
      - 0
      - 0
      - 0
      lastTotalSize: 0
      splitSize: 6
      xOffset: 0
      m_Version: 1
      oldRealSizes: 
      oldMinSizes: 
      oldMaxSizes: 
      oldSplitSize: 0
  m_LayoutStripsOnRight:
    m_VerticalSplitter:
      ID: 0
      splitterInitialOffset: 0
      currentActiveSplitter: -1
      realSizes:
      - 60
      - 60
      - 60
      - 60
      relativeSizes:
      - 0.25
      - 0.25
      - 0.25
      - 0.25
      minSizes:
      - 100
      - 85
      - 85
      - 85
      maxSizes:
      - 0
      - 0
      - 0
      - 0
      lastTotalSize: 0
      splitSize: 6
      xOffset: 0
      m_Version: 1
      oldRealSizes: 
      oldMinSizes: 
      oldMaxSizes: 
      oldSplitSize: 0
    m_HorizontalSplitter:
      ID: 115386
      splitterInitialOffset: 0
      currentActiveSplitter: -1
      realSizes:
      - 518
      - 1208
      relativeSizes:
      - 0.3
      - 0.7
      minSizes:
      - 160
      - 160
      maxSizes:
      - 0
      - 0
      lastTotalSize: 1726
      splitSize: 6
      xOffset: 0
      m_Version: 1
      oldRealSizes: 
      oldMinSizes: 
      oldMaxSizes: 
      oldSplitSize: 0
  m_SectionOrder: 00000000030000000100000002000000
  m_LayoutMode: 1
  m_SortGroupsAlphabetically: 0
  m_ShowReferencedBuses: 1
  m_ShowBusConnections: 0
  m_ShowBusConnectionsOfSelection: 0
--- !u!114 &13
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 13999, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_MinSize: {x: 200, y: 200}
  m_MaxSize: {x: 4000, y: 4000}
  m_TitleContent:
    m_Text: UI Builder
    m_Image: {fileID: 8683992553321208622, guid: 0000000000000000d000000000000000, type: 0}
    m_Tooltip: 
    m_TextWithWhitespace: "UI Builder\u200B"
  m_Pos:
    serializedVersion: 2
    x: 837
    y: 110
    width: 1690
    height: 999
  m_SerializedDataModeController:
    m_DataMode: 0
    m_PreferredDataMode: 0
    m_SupportedDataModes: 
    isAutomatic: 1
  m_ViewDataDictionary: {fileID: 0}
  m_OverlayCanvas:
    m_LastAppliedPresetName: Default
    m_SaveData: []
    m_ContainerData: []
    m_OverlaysVisible: 1
--- !u!114 &14
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 6629f1bb292b749a18b5fff7994c8b19, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_MinSize: {x: 600, y: 350}
  m_MaxSize: {x: 4000, y: 4000}
  m_TitleContent:
    m_Text: Unity Version Control
    m_Image: {fileID: 0}
    m_Tooltip: 
    m_TextWithWhitespace: "Unity Version Control\u200B"
  m_Pos:
    serializedVersion: 2
    x: 895
    y: 111
    width: 1690
    height: 999
  m_SerializedDataModeController:
    m_DataMode: 0
    m_PreferredDataMode: 0
    m_SupportedDataModes: 
    isAutomatic: 1
  m_ViewDataDictionary: {fileID: 0}
  m_OverlayCanvas:
    m_LastAppliedPresetName: Default
    m_SaveData: []
    m_ContainerData: []
    m_OverlaysVisible: 1
  mForceToReOpen: 0
--- !u!114 &15
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 12013, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_MinSize: {x: 50, y: 50}
  m_MaxSize: {x: 4000, y: 4000}
  m_TitleContent:
    m_Text: Scene
    m_Image: {fileID: 2593428753322112591, guid: 0000000000000000d000000000000000, type: 0}
    m_Tooltip: 
    m_TextWithWhitespace: "Scene\u200B"
  m_Pos:
    serializedVersion: 2
    x: 493
    y: 109
    width: 1690
    height: 999
  m_SerializedDataModeController:
    m_DataMode: 0
    m_PreferredDataMode: 0
    m_SupportedDataModes: 
    isAutomatic: 1
  m_ViewDataDictionary: {fileID: 0}
  m_OverlayCanvas:
    m_LastAppliedPresetName: Default
    m_SaveData:
    - dockPosition: 0
      containerId: overlay-container--right
      displayed: 1
      id: Tool Settings
      index: 2
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":-311.0,"y":146.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":1,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: -311, y: 146}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 1
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 0
      containerId: overlay-container--right
      displayed: 1
      id: unity-grid-and-snap-toolbar
      index: 5
      contents: '{"m_Layout":1,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":-145.0,"y":-60.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":3,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: -145, y: -60}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 3
      layout: 1
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 0
      containerId: overlay-container--right
      displayed: 1
      id: unity-scene-view-toolbar
      index: 3
      contents: '{"m_Layout":1,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":-290.0009765625,"y":-173.9990234375},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":3,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: -290.00098, y: -173.99902}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 3
      layout: 1
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-toolbar__top
      displayed: 0
      id: unity-search-toolbar
      index: 0
      contents: '{"m_Layout":1,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":-241.0,"y":0.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":1,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: -241, y: 0}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 1
      layout: 1
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 0
      containerId: overlay-container--right
      displayed: 1
      id: unity-transform-toolbar
      index: 1
      contents: '{"m_Layout":1,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":24.0,"y":25.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 24, y: 25}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 1
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 0
      containerId: overlay-container--left
      displayed: 1
      id: unity-component-tools
      index: 1
      contents: 
      floating: 0
      collapsed: 0
      snapOffset: {x: 0, y: 197}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 2
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 0
      containerId: Floating
      displayed: 0
      id: Orientation
      index: 1
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Floating":true,"m_FloatingSnapOffset":{"x":-111.0,"y":163.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":1,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 1
      collapsed: 0
      snapOffset: {x: -111, y: 163}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 1
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: Scene View/Light Settings
      index: 0
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":0.0,"y":0.0},"m_SnapOffsetDelta":{"x":24.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 0, y: 0}
      snapOffsetDelta: {x: 24, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: Scene View/Camera
      index: 1
      contents: 
      floating: 0
      collapsed: 0
      snapOffset: {x: 0, y: 0}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: Scene View/Cloth Constraints
      index: 1
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":0.0,"y":0.0},"m_SnapOffsetDelta":{"x":24.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 0, y: 0}
      snapOffsetDelta: {x: 24, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: Scene View/Cloth Collisions
      index: 2
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":0.0,"y":0.0},"m_SnapOffsetDelta":{"x":24.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 0, y: 0}
      snapOffsetDelta: {x: 24, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: Scene View/Navmesh Display
      index: 4
      contents: 
      floating: 0
      collapsed: 0
      snapOffset: {x: 0, y: 0}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: Scene View/Agent Display
      index: 5
      contents: 
      floating: 0
      collapsed: 0
      snapOffset: {x: 0, y: 0}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: Scene View/Obstacle Display
      index: 6
      contents: 
      floating: 0
      collapsed: 0
      snapOffset: {x: 0, y: 0}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: Scene View/Occlusion Culling
      index: 3
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":24.0,"y":0.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 24, y: 0}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: Scene View/Physics Debugger
      index: 4
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":0.0,"y":0.0},"m_SnapOffsetDelta":{"x":24.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 0, y: 0}
      snapOffsetDelta: {x: 24, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: Scene View/Scene Visibility
      index: 5
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":0.0,"y":0.0},"m_SnapOffsetDelta":{"x":24.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 0, y: 0}
      snapOffsetDelta: {x: 24, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: Scene View/Particles
      index: 6
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":24.0,"y":0.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 24, y: 0}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: Scene View/Tilemap
      index: 11
      contents: 
      floating: 0
      collapsed: 0
      snapOffset: {x: 0, y: 0}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: Scene View/Tilemap Palette Helper
      index: 12
      contents: 
      floating: 0
      collapsed: 0
      snapOffset: {x: 0, y: 0}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 0
      containerId: overlay-toolbar__top
      displayed: 0
      id: Brush Attributes
      index: 0
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":48.0,"y":48.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 48, y: 48}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 0
      containerId: overlay-toolbar__left
      displayed: 0
      id: Terrain Tools
      index: 0
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":48.0,"y":48.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 48, y: 48}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 0
      containerId: overlay-toolbar__left
      displayed: 0
      id: Brush Masks
      index: 1
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":48.0,"y":48.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 48, y: 48}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: Scene View/Open Tile Palette
      index: 0
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":48.0,"y":48.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 48, y: 48}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: Scene View/Tile Palette Clipboard
      index: 1
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":48.0,"y":48.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":390.0,"y":300.0},"m_SizeOverridden":true}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 48, y: 48}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 390, y: 300}
      sizeOverridden: 1
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: Scene View/Tilemap Focus
      index: 2
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":48.0,"y":48.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 48, y: 48}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: Scene View/Tile Palette Brush Pick
      index: 4
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":48.0,"y":48.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":390.0,"y":300.0},"m_SizeOverridden":true}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 48, y: 48}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 390, y: 300}
      sizeOverridden: 1
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: APV Overlay
      index: 7
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":48.0,"y":48.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 48, y: 48}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: Scene View/Path
      index: 12
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":48.0,"y":48.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 48, y: 48}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: Scene View/Sprite Resolver
      index: 13
      contents: 
      floating: 0
      collapsed: 0
      snapOffset: {x: 48, y: 48}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 236, y: 152}
      sizeOverridden: 1
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: Cinemachine Tool Settings
      index: 14
      contents: 
      floating: 0
      collapsed: 0
      snapOffset: {x: 48, y: 48}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: Scene View/TrailRenderer
      index: 8
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":48.0,"y":48.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 48, y: 48}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 1
      id: UnityEditor.SceneViewCameraOverlay
      index: 16
      contents: 
      floating: 0
      collapsed: 0
      snapOffset: {x: 48, y: 48}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 240, y: 135}
      sizeOverridden: 1
    - dockPosition: 0
      containerId: overlay-container--right
      displayed: 1
      id: unity-scene-view-camera-mode-toolbar
      index: 4
      contents: '{"m_Layout":1,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":-196.0,"y":-30.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":3,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: -196, y: -30}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 3
      layout: 1
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--left
      displayed: 0
      id: Scene View/Lighting Visualization Colors
      index: 0
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":0.0,"y":0.0},"m_SnapOffsetDelta":{"x":24.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 0, y: 0}
      snapOffsetDelta: {x: 24, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 0
      containerId: overlay-container--right
      displayed: 1
      id: Overlays/OverlayMenu
      index: 0
      contents: '{"m_Layout":1,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":24.0,"y":25.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 24, y: 25}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 1
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: Scene View/Sprite Swap
      index: 15
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":0.0,"y":0.0},"m_SnapOffsetDelta":{"x":24.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 0, y: 0}
      snapOffsetDelta: {x: 24, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 0
      containerId: Floating
      displayed: 0
      id: SceneView/CamerasOverlay
      index: 0
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Floating":true,"m_FloatingSnapOffset":{"x":-559.0,"y":-426.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":3,"m_Size":{"x":542.0,"y":387.0},"m_SizeOverridden":true}'
      floating: 1
      collapsed: 0
      snapOffset: {x: -559, y: -426}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 3
      layout: 4
      size: {x: 542, y: 387}
      sizeOverridden: 1
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: Scene View/PBR Validation Settings
      index: 9
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":0.0,"y":0.0},"m_SnapOffsetDelta":{"x":24.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 0, y: 0}
      snapOffsetDelta: {x: 24, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 1
      id: LDtkWorldDepthGUIDrawer
      index: 0
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":-134.0009765625,"y":-77.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":3,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: -134.00098, y: -77}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 3
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: Scene View/Visual Effect Event Tester
      index: 10
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":0.0,"y":0.0},"m_SnapOffsetDelta":{"x":24.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 0, y: 0}
      snapOffsetDelta: {x: 24, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: Scene View/Visual Effect Timeline Control
      index: 11
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":0.0,"y":0.0},"m_SnapOffsetDelta":{"x":24.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 0, y: 0}
      snapOffsetDelta: {x: 24, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: Scene View/Visual Effect
      index: 12
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":0.0,"y":0.0},"m_SnapOffsetDelta":{"x":24.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 0, y: 0}
      snapOffsetDelta: {x: 24, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: Scene View/Visual Effect Model
      index: 13
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":0.0,"y":0.0},"m_SnapOffsetDelta":{"x":24.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 0, y: 0}
      snapOffsetDelta: {x: 24, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 0
      containerId: Floating
      displayed: 1
      id: AINavigationOverlay
      index: 0
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Floating":true,"m_FloatingSnapOffset":{"x":-214.0009765625,"y":-242.0009765625},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":3,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 1
      collapsed: 0
      snapOffset: {x: -214.00098, y: -242.00098}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 3
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 0
      containerId: overlay-toolbar__top
      displayed: 0
      id: Brush Attributes (0)
      index: 3
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":0.0,"y":0.0},"m_SnapOffsetDelta":{"x":24.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 0, y: 0}
      snapOffsetDelta: {x: 24, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 0
      containerId: overlay-toolbar__left
      displayed: 0
      id: Brush Filters
      index: 1
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":24.0,"y":25.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 24, y: 25}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: brush-info-overlay
      index: 10
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":24.0,"y":25.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 24, y: 25}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: unity-spline-inspector
      index: 13
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":0.0,"y":0.0},"m_SnapOffsetDelta":{"x":24.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 0, y: 0}
      snapOffsetDelta: {x: 24, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: Scene View/Animation Rigging
      index: 12
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":24.0,"y":25.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 24, y: 25}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: COZY Tools
      index: 13
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":0.0,"y":0.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 0, y: 0}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 1
      id: Hot Reload
      index: 15
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":24.0,"y":0.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 24, y: 0}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 1
      id: Chisel Snap Values
      index: 15
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":24.0,"y":0.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 24, y: 0}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 1
      id: Chisel Snap Settings
      index: 16
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":24.0,"y":0.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 24, y: 0}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: Placement Options
      index: 17
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":24.0,"y":0.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 24, y: 0}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 1
      id: Chisel Global
      index: 18
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":24.0,"y":0.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 24, y: 0}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 1
      id: Chisel Active Generator
      index: 19
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":-309.0,"y":-407.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":3,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: -309, y: -407}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 3
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: Surface Options
      index: 20
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":0.0,"y":0.0},"m_SnapOffsetDelta":{"x":24.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 0, y: 0}
      snapOffsetDelta: {x: 24, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 1
      id: UnityEditor.ProBuilder.MenuActionSettingsOverlay
      index: 15
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":24.0,"y":0.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 24, y: 0}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: ProBuilderTools
      index: 15
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":24.0,"y":0.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 24, y: 0}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    m_ContainerData:
    - containerId: overlay-toolbar__top
      scrollOffset: 0
    - containerId: overlay-toolbar__left
      scrollOffset: 0
    - containerId: overlay-container--left
      scrollOffset: 0
    - containerId: overlay-container--right
      scrollOffset: 0
    - containerId: overlay-toolbar__right
      scrollOffset: 0
    - containerId: overlay-toolbar__bottom
      scrollOffset: 0
    - containerId: Floating
      scrollOffset: 0
    m_OverlaysVisible: 1
  m_WindowGUID: 0ff93da3bfc969f469acc5aaf28523d1
  m_Gizmos: 1
  m_OverrideSceneCullingMask: 6917529027641081856
  m_SceneIsLit: 1
  m_SceneLighting: 1
  m_2DMode: 0
  m_isRotationLocked: 0
  m_PlayAudio: 0
  m_AudioPlay: 0
  m_DebugDrawModesUseInteractiveLightBakingData: 0
  m_Position:
    m_Target: {x: -67.78896, y: 5.930114, z: 41.628845}
    speed: 2
    m_Value: {x: -67.78896, y: 5.930114, z: 41.628845}
  m_RenderMode: 0
  m_CameraMode:
    drawMode: 0
    name: Shaded
    section: Shading Mode
  m_ValidateTrueMetals: 0
  m_DoValidateTrueMetals: 0
  m_SceneViewState:
    m_AlwaysRefresh: 0
    showFog: 1
    showSkybox: 1
    showFlares: 1
    showImageEffects: 1
    showParticleSystems: 1
    showVisualEffectGraphs: 1
    m_FxEnabled: 1
  m_Grid:
    xGrid:
      m_Fade:
        m_Target: 0
        speed: 2
        m_Value: 0
      m_Color: {r: 0.5, g: 0.5, b: 0.5, a: 0.4}
      m_Pivot: {x: 0, y: 0, z: 0}
      m_Size: {x: 0, y: 0}
    yGrid:
      m_Fade:
        m_Target: 0
        speed: 2
        m_Value: 0
      m_Color: {r: 0.5, g: 0.5, b: 0.5, a: 0.4}
      m_Pivot: {x: 0, y: 0, z: 0}
      m_Size: {x: 1, y: 1}
    zGrid:
      m_Fade:
        m_Target: 0
        speed: 2
        m_Value: 0
      m_Color: {r: 0.5, g: 0.5, b: 0.5, a: 0.4}
      m_Pivot: {x: 0, y: 0, z: 0}
      m_Size: {x: 0, y: 0}
    m_ShowGrid: 0
    m_GridAxis: 1
    m_gridOpacity: 0.5
  m_Rotation:
    m_Target: {x: -0.014252106, y: 0.5365496, z: -0.005732921, w: -0.8463336}
    speed: 2
    m_Value: {x: -0.014220838, y: 0.5353725, z: -0.0057203434, w: -0.8444769}
  m_Size:
    m_Target: 58.58941
    speed: 2
    m_Value: 58.58941
  m_Ortho:
    m_Target: 0
    speed: 2
    m_Value: 0
  m_CameraSettings:
    m_Speed: 2
    m_SpeedNormalized: 1
    m_SpeedMin: 0.01
    m_SpeedMax: 2
    m_EasingEnabled: 1
    m_EasingDuration: 0.4
    m_AccelerationEnabled: 1
    m_FieldOfViewHorizontalOrVertical: 60
    m_NearClip: 0.03
    m_FarClip: 10000
    m_DynamicClip: 1
    m_OcclusionCulling: 0
  m_LastSceneViewRotation: {x: 0.068913944, y: -0.6239462, z: 0.050874937, w: 0.77913475}
  m_LastSceneViewOrtho: 0
  m_Viewpoint:
    m_SceneView: {fileID: 15}
    m_CameraOverscanSettings:
      m_Opacity: 50
      m_Scale: 1
  m_ReplacementShader: {fileID: 0}
  m_ReplacementString: 
  m_SceneVisActive: 1
  m_LastLockedObject: {fileID: 0}
  m_LastDebugDrawMode:
    drawMode: 35
    name: 
    section: 
  m_ViewIsLockedToObject: 0
--- !u!114 &16
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 924ffcbe75518854f97b48776d0f1939, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_MinSize: {x: 50, y: 50}
  m_MaxSize: {x: 4000, y: 4000}
  m_TitleContent:
    m_Text: Volumetric Heart
    m_Image: {fileID: 2800000, guid: 7129268cf102b2f45809905bcb27ce8b, type: 3}
    m_Tooltip: 
    m_TextWithWhitespace: "Volumetric Heart\u200B"
  m_Pos:
    serializedVersion: 2
    x: 427
    y: 79
    width: 2364
    height: 1058
  m_SerializedDataModeController:
    m_DataMode: 0
    m_PreferredDataMode: 0
    m_SupportedDataModes: 
    isAutomatic: 1
  m_ViewDataDictionary: {fileID: 0}
  m_OverlayCanvas:
    m_LastAppliedPresetName: Default
    m_SaveData: []
    m_ContainerData: []
    m_OverlaysVisible: 1
  m_Selected: 31c75348c6538d447a918cbc5ec8c202
  m_GraphObject: {fileID: 0}
  m_LastSerializedFileContents: "{\n    \"m_SGVersion\": 3,\n    \"m_Type\": \"UnityEditor.ShaderGraph.GraphData\",\n   
    \"m_ObjectId\": \"5cbfbea510bc4bc8aeee844d00799a5b\",\n    \"m_Properties\":
    [\n        {\n            \"m_Id\": \"d5c36d12d27240f7ab75d640fafab609\"\n       
    },\n        {\n            \"m_Id\": \"39640db60ba8486caa16e6de768e9a15\"\n       
    },\n        {\n            \"m_Id\": \"8de610096d3d46caa3fa4c411485ca63\"\n       
    },\n        {\n            \"m_Id\": \"e62d302b15a84324bd038795690c6c4f\"\n       
    },\n        {\n            \"m_Id\": \"4e659e00b03444d6b6b50065b170dee2\"\n       
    }\n    ],\n    \"m_Keywords\": [],\n    \"m_Dropdowns\": [],\n    \"m_CategoryData\":
    [\n        {\n            \"m_Id\": \"b60bd87d1f314dbc8ba2a76f2f91acec\"\n       
    }\n    ],\n    \"m_Nodes\": [\n        {\n            \"m_Id\": \"41275c9030fa4cc9a20b05bfb03cb7a0\"\n       
    },\n        {\n            \"m_Id\": \"e8ee9a36bc9b45358b78d91c6cc14fe5\"\n       
    },\n        {\n            \"m_Id\": \"8b534236256649ecb06f03eb3b88a11b\"\n       
    },\n        {\n            \"m_Id\": \"bdaec961bc39453c950cd9c9b06d31e3\"\n       
    },\n        {\n            \"m_Id\": \"1c05ef861e56409e89c43bac2fce7d2d\"\n       
    },\n        {\n            \"m_Id\": \"504f3b650b084582864b6fa1392cd5d3\"\n       
    },\n        {\n            \"m_Id\": \"9e31da9e949f4c0691e368688bc932ec\"\n       
    },\n        {\n            \"m_Id\": \"3cdde6db00cf4017b29f25283f6e3600\"\n       
    },\n        {\n            \"m_Id\": \"67338e132b0944f481cbe668675fc9e3\"\n       
    },\n        {\n            \"m_Id\": \"d5c2e31468114a27bdbf49a716f00d7f\"\n       
    },\n        {\n            \"m_Id\": \"595b5bb08eb4494380c15eb7231482b3\"\n       
    },\n        {\n            \"m_Id\": \"d3aa7700a1fa4a7eab32325ed6bf8b37\"\n       
    },\n        {\n            \"m_Id\": \"ac3520b082ef413b8ab5954817163874\"\n       
    },\n        {\n            \"m_Id\": \"b8616360f4604ae080f5ef8ddd7af920\"\n       
    }\n    ],\n    \"m_GroupDatas\": [],\n    \"m_StickyNoteDatas\": [],\n    \"m_Edges\":
    [\n        {\n            \"m_OutputSlot\": {\n                \"m_Node\": {\n                   
    \"m_Id\": \"8b534236256649ecb06f03eb3b88a11b\"\n                },\n               
    \"m_SlotId\": 0\n            },\n            \"m_InputSlot\": {\n               
    \"m_Node\": {\n                    \"m_Id\": \"504f3b650b084582864b6fa1392cd5d3\"\n               
    },\n                \"m_SlotId\": 0\n            }\n        },\n        {\n           
    \"m_OutputSlot\": {\n                \"m_Node\": {\n                    \"m_Id\":
    \"bdaec961bc39453c950cd9c9b06d31e3\"\n                },\n                \"m_SlotId\":
    0\n            },\n            \"m_InputSlot\": {\n                \"m_Node\":
    {\n                    \"m_Id\": \"1c05ef861e56409e89c43bac2fce7d2d\"\n               
    },\n                \"m_SlotId\": 0\n            }\n        },\n        {\n           
    \"m_OutputSlot\": {\n                \"m_Node\": {\n                    \"m_Id\":
    \"b8616360f4604ae080f5ef8ddd7af920\"\n                },\n                \"m_SlotId\":
    0\n            },\n            \"m_InputSlot\": {\n                \"m_Node\":
    {\n                    \"m_Id\": \"1c05ef861e56409e89c43bac2fce7d2d\"\n               
    },\n                \"m_SlotId\": 1\n            }\n        },\n        {\n           
    \"m_OutputSlot\": {\n                \"m_Node\": {\n                    \"m_Id\":
    \"1c05ef861e56409e89c43bac2fce7d2d\"\n                },\n                \"m_SlotId\":
    2\n            },\n            \"m_InputSlot\": {\n                \"m_Node\":
    {\n                    \"m_Id\": \"3cdde6db00cf4017b29f25283f6e3600\"\n               
    },\n                \"m_SlotId\": 1\n            }\n        },\n        {\n           
    \"m_OutputSlot\": {\n                \"m_Node\": {\n                    \"m_Id\":
    \"504f3b650b084582864b6fa1392cd5d3\"\n                },\n                \"m_SlotId\":
    1\n            },\n            \"m_InputSlot\": {\n                \"m_Node\":
    {\n                    \"m_Id\": \"9e31da9e949f4c0691e368688bc932ec\"\n               
    },\n                \"m_SlotId\": 0\n            }\n        },\n        {\n           
    \"m_OutputSlot\": {\n                \"m_Node\": {\n                    \"m_Id\":
    \"3cdde6db00cf4017b29f25283f6e3600\"\n                },\n                \"m_SlotId\":
    2\n            },\n            \"m_InputSlot\": {\n                \"m_Node\":
    {\n                    \"m_Id\": \"9e31da9e949f4c0691e368688bc932ec\"\n               
    },\n                \"m_SlotId\": 0\n            }\n        },\n        {\n           
    \"m_OutputSlot\": {\n                \"m_Node\": {\n                    \"m_Id\":
    \"9e31da9e949f4c0691e368688bc932ec\"\n                },\n                \"m_SlotId\":
    1\n            },\n            \"m_InputSlot\": {\n                \"m_Node\":
    {\n                    \"m_Id\": \"67338e132b0944f481cbe668675fc9e3\"\n               
    },\n                \"m_SlotId\": 2\n            }\n        },\n        {\n           
    \"m_OutputSlot\": {\n                \"m_Node\": {\n                    \"m_Id\":
    \"595b5bb08eb4494380c15eb7231482b3\"\n                },\n                \"m_SlotId\":
    0\n            },\n            \"m_InputSlot\": {\n                \"m_Node\":
    {\n                    \"m_Id\": \"67338e132b0944f481cbe668675fc9e3\"\n               
    },\n                \"m_SlotId\": 1\n            }\n        },\n        {\n           
    \"m_OutputSlot\": {\n                \"m_Node\": {\n                    \"m_Id\":
    \"67338e132b0944f481cbe668675fc9e3\"\n                },\n                \"m_SlotId\":
    3\n            },\n            \"m_InputSlot\": {\n                \"m_Node\":
    {\n                    \"m_Id\": \"d5c2e31468114a27bdbf49a716f00d7f\"\n               
    },\n                \"m_SlotId\": 2\n            }\n        },\n        {\n           
    \"m_OutputSlot\": {\n                \"m_Node\": {\n                    \"m_Id\":
    \"d3aa7700a1fa4a7eab32325ed6bf8b37\"\n                },\n                \"m_SlotId\":
    0\n            },\n            \"m_InputSlot\": {\n                \"m_Node\":
    {\n                    \"m_Id\": \"d5c2e31468114a27bdbf49a716f00d7f\"\n               
    },\n                \"m_SlotId\": 0\n            }\n        },\n        {\n           
    \"m_OutputSlot\": {\n                \"m_Node\": {\n                    \"m_Id\":
    \"ac3520b082ef413b8ab5954817163874\"\n                },\n                \"m_SlotId\":
    0\n            },\n            \"m_InputSlot\": {\n                \"m_Node\":
    {\n                    \"m_Id\": \"d5c2e31468114a27bdbf49a716f00d7f\"\n               
    },\n                \"m_SlotId\": 1\n            }\n        },\n        {\n           
    \"m_OutputSlot\": {\n                \"m_Node\": {\n                    \"m_Id\":
    \"d5c2e31468114a27bdbf49a716f00d7f\"\n                },\n                \"m_SlotId\":
    3\n            },\n            \"m_InputSlot\": {\n                \"m_Node\":
    {\n                    \"m_Id\": \"41275c9030fa4cc9a20b05bfb03cb7a0\"\n               
    },\n                \"m_SlotId\": 0\n            }\n        },\n        {\n           
    \"m_OutputSlot\": {\n                \"m_Node\": {\n                    \"m_Id\":
    \"67338e132b0944f481cbe668675fc9e3\"\n                },\n                \"m_SlotId\":
    3\n            },\n            \"m_InputSlot\": {\n                \"m_Node\":
    {\n                    \"m_Id\": \"e8ee9a36bc9b45358b78d91c6cc14fe5\"\n               
    },\n                \"m_SlotId\": 0\n            }\n        }\n    ],\n    \"m_VertexContext\":
    {\n        \"m_Position\": {\n            \"x\": -1264.6669921875,\n           
    \"y\": -134.00001525878907\n        },\n        \"m_Blocks\": []\n    },\n   
    \"m_FragmentContext\": {\n        \"m_Position\": {\n            \"x\": -1264.6669921875,\n           
    \"y\": 65.99992370605469\n        },\n        \"m_Blocks\": [\n            {\n               
    \"m_Id\": \"41275c9030fa4cc9a20b05bfb03cb7a0\"\n            },\n            {\n               
    \"m_Id\": \"e8ee9a36bc9b45358b78d91c6cc14fe5\"\n            }\n        ]\n   
    },\n    \"m_PreviewData\": {\n        \"serializedMesh\": {\n            \"m_SerializedMesh\":
    \"{\\\"mesh\\\":{\\\"instanceID\\\":0}}\",\n            \"m_Guid\": \"\"\n       
    },\n        \"preventRotation\": false\n    },\n    \"m_Path\": \"HDRPSamples\",\n   
    \"m_GraphPrecision\": 1,\n    \"m_PreviewMode\": 2,\n    \"m_OutputNode\": {\n       
    \"m_Id\": \"\"\n    },\n    \"m_ActiveTargets\": [\n        {\n            \"m_Id\":
    \"8ea1929a43394e3a9d3644bd98b99880\"\n        }\n    ]\n}\n\n{\n    \"m_SGVersion\":
    1,\n    \"m_Type\": \"UnityEditor.ShaderGraph.Internal.Vector1ShaderProperty\",\n   
    \"m_ObjectId\": \"d5c36d12d27240f7ab75d640fafab609\",\n    \"m_Guid\": {\n       
    \"m_GuidSerialized\": \"d94296b4-7e0e-42d0-9335-9ac650fd151d\"\n    },\n    \"m_Name\":
    \"Noise Scale\",\n    \"m_DefaultRefNameVersion\": 1,\n    \"m_RefNameGeneratedByDisplayName\":
    \"Noise Scale\",\n    \"m_DefaultReferenceName\": \"_Noise_Scale\",\n    \"m_OverrideReferenceName\":
    \"\",\n    \"m_GeneratePropertyBlock\": true,\n    \"m_UseCustomSlotLabel\":
    false,\n    \"m_CustomSlotLabel\": \"\",\n    \"m_DismissedVersion\": 0,\n   
    \"m_Precision\": 0,\n    \"overrideHLSLDeclaration\": false,\n    \"hlslDeclarationOverride\":
    0,\n    \"m_Hidden\": false,\n    \"m_Value\": 2.5,\n    \"m_FloatType\": 0,\n   
    \"m_RangeValues\": {\n        \"x\": 0.1,\n        \"y\": 10.0\n    }\n}\n\n{\n   
    \"m_SGVersion\": 1,\n    \"m_Type\": \"UnityEditor.ShaderGraph.Internal.Vector1ShaderProperty\",\n   
    \"m_ObjectId\": \"39640db60ba8486caa16e6de768e9a15\",\n    \"m_Guid\": {\n       
    \"m_GuidSerialized\": \"3172dd60-7a28-4564-8b94-8ed12f430dee\"\n    },\n    \"m_Name\":
    \"Fog Density\",\n    \"m_DefaultRefNameVersion\": 1,\n    \"m_RefNameGeneratedByDisplayName\":
    \"Fog Density\",\n    \"m_DefaultReferenceName\": \"_Fog_Density\",\n    \"m_OverrideReferenceName\":
    \"\",\n    \"m_GeneratePropertyBlock\": true,\n    \"m_UseCustomSlotLabel\":
    false,\n    \"m_CustomSlotLabel\": \"\",\n    \"m_DismissedVersion\": 0,\n   
    \"m_Precision\": 0,\n    \"overrideHLSLDeclaration\": false,\n    \"hlslDeclarationOverride\":
    0,\n    \"m_Hidden\": false,\n    \"m_Value\": 0.6,\n    \"m_FloatType\": 1,\n   
    \"m_RangeValues\": {\n        \"x\": 0.0,\n        \"y\": 1.0\n    }\n}\n\n{\n   
    \"m_SGVersion\": 3,\n    \"m_Type\": \"UnityEditor.ShaderGraph.Internal.ColorShaderProperty\",\n   
    \"m_ObjectId\": \"8de610096d3d46caa3fa4c411485ca63\",\n    \"m_Guid\": {\n       
    \"m_GuidSerialized\": \"04ef0f66-ccd4-4379-baa4-6c31b289478f\"\n    },\n    \"m_Name\":
    \"Fog Color Deep\",\n    \"m_DefaultRefNameVersion\": 1,\n    \"m_RefNameGeneratedByDisplayName\":
    \"Fog Color Deep\",\n    \"m_DefaultReferenceName\": \"_Fog_Color_Deep\",\n   
    \"m_OverrideReferenceName\": \"\",\n    \"m_GeneratePropertyBlock\": true,\n   
    \"m_UseCustomSlotLabel\": false,\n    \"m_CustomSlotLabel\": \"\",\n    \"m_DismissedVersion\":
    0,\n    \"m_Precision\": 0,\n    \"overrideHLSLDeclaration\": false,\n    \"hlslDeclarationOverride\":
    0,\n    \"m_Hidden\": false,\n    \"m_Value\": {\n        \"r\": 0.2,\n       
    \"g\": 0.4,\n        \"b\": 0.8,\n        \"a\": 1.0\n    },\n    \"isMainColor\":
    false,\n    \"m_ColorMode\": 0\n}\n\n{\n    \"m_SGVersion\": 3,\n    \"m_Type\":
    \"UnityEditor.ShaderGraph.Internal.ColorShaderProperty\",\n    \"m_ObjectId\":
    \"e62d302b15a84324bd038795690c6c4f\",\n    \"m_Guid\": {\n        \"m_GuidSerialized\":
    \"89623951-e4e0-4e8c-b118-dab98d6b7ac5\"\n    },\n    \"m_Name\": \"Fog Color
    Light\",\n    \"m_DefaultRefNameVersion\": 1,\n    \"m_RefNameGeneratedByDisplayName\":
    \"Fog Color Light\",\n    \"m_DefaultReferenceName\": \"_Fog_Color_Light\",\n   
    \"m_OverrideReferenceName\": \"\",\n    \"m_GeneratePropertyBlock\": true,\n   
    \"m_UseCustomSlotLabel\": false,\n    \"m_CustomSlotLabel\": \"\",\n    \"m_DismissedVersion\":
    0,\n    \"m_Precision\": 0,\n    \"overrideHLSLDeclaration\": false,\n    \"hlslDeclarationOverride\":
    0,\n    \"m_Hidden\": false,\n    \"m_Value\": {\n        \"r\": 0.9,\n       
    \"g\": 0.95,\n        \"b\": 1.0,\n        \"a\": 1.0\n    },\n    \"isMainColor\":
    false,\n    \"m_ColorMode\": 0\n}\n\n{\n    \"m_SGVersion\": 1,\n    \"m_Type\":
    \"UnityEditor.ShaderGraph.Internal.Vector1ShaderProperty\",\n    \"m_ObjectId\":
    \"4e659e00b03444d6b6b50065b170dee2\",\n    \"m_Guid\": {\n        \"m_GuidSerialized\":
    \"483c418e-1543-4a31-8276-f882e66d9e42\"\n    },\n    \"m_Name\": \"Flow Speed\",\n   
    \"m_DefaultRefNameVersion\": 1,\n    \"m_RefNameGeneratedByDisplayName\": \"Flow
    Speed\",\n    \"m_DefaultReferenceName\": \"_Flow_Speed\",\n    \"m_OverrideReferenceName\":
    \"\",\n    \"m_GeneratePropertyBlock\": true,\n    \"m_UseCustomSlotLabel\":
    false,\n    \"m_CustomSlotLabel\": \"\",\n    \"m_DismissedVersion\": 0,\n   
    \"m_Precision\": 0,\n    \"overrideHLSLDeclaration\": false,\n    \"hlslDeclarationOverride\":
    0,\n    \"m_Hidden\": false,\n    \"m_Value\": 0.5,\n    \"m_FloatType\": 0,\n   
    \"m_RangeValues\": {\n        \"x\": 0.0,\n        \"y\": 5.0\n    }\n}\n\n{\n   
    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.UVNode\",\n   
    \"m_ObjectId\": \"8b534236256649ecb06f03eb3b88a11b\",\n    \"m_Group\": {\n       
    \"m_Id\": \"\"\n    },\n    \"m_Name\": \"UV\",\n    \"m_DrawState\": {\n       
    \"m_Expanded\": true,\n        \"m_Position\": {\n            \"serializedVersion\":
    \"2\",\n            \"x\": -2200.0,\n            \"y\": 200.0,\n            \"width\":
    150.0,\n            \"height\": 135.0\n        }\n    },\n    \"m_Slots\": [\n       
    {\n            \"m_Id\": \"d4bd3f378f2c4685a81cfa19793dc0f7\"\n        }\n   
    ],\n    \"synonyms\": [\n        \"texcoords\",\n        \"coords\", \n       
    \"coordinates\"\n    ],\n    \"m_Precision\": 0,\n    \"m_PreviewExpanded\":
    false,\n    \"m_DismissedVersion\": 0,\n    \"m_PreviewMode\": 0,\n    \"m_CustomColors\":
    {\n        \"m_SerializableColors\": []\n    },\n    \"m_OutputChannel\": 0\n}\n\n{\n   
    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.TimeNode\",\n   
    \"m_ObjectId\": \"bdaec961bc39453c950cd9c9b06d31e3\",\n    \"m_Group\": {\n       
    \"m_Id\": \"\"\n    },\n    \"m_Name\": \"Time\",\n    \"m_DrawState\": {\n       
    \"m_Expanded\": true,\n        \"m_Position\": {\n            \"serializedVersion\":
    \"2\",\n            \"x\": -2200.0,\n            \"y\": 400.0,\n            \"width\":
    125.0,\n            \"height\": 175.0\n        }\n    },\n    \"m_Slots\": [\n       
    {\n            \"m_Id\": \"b24e008a74a448cb84ed09d34f689359\"\n        },\n       
    {\n            \"m_Id\": \"76516320ee8a4f81956f5a30d51e962c\"\n        },\n       
    {\n            \"m_Id\": \"88ea5118b5b54c95a61ef9a95276b3de\"\n        },\n       
    {\n            \"m_Id\": \"fa2095111b464fd39674131453b6be0d\"\n        },\n       
    {\n            \"m_Id\": \"129d5c7fadc7433abad14b758a90a748\"\n        }\n   
    ],\n    \"synonyms\": [],\n    \"m_Precision\": 0,\n    \"m_PreviewExpanded\":
    true,\n    \"m_DismissedVersion\": 0,\n    \"m_PreviewMode\": 0,\n    \"m_CustomColors\":
    {\n        \"m_SerializableColors\": []\n    }\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.MultiplyNode\",\n    \"m_ObjectId\":
    \"1c05ef861e56409e89c43bac2fce7d2d\",\n    \"m_Group\": {\n        \"m_Id\":
    \"\"\n    },\n    \"m_Name\": \"Multiply\",\n    \"m_DrawState\": {\n       
    \"m_Expanded\": true,\n        \"m_Position\": {\n            \"serializedVersion\":
    \"2\",\n            \"x\": -1950.0,\n            \"y\": 450.0,\n            \"width\":
    130.0,\n            \"height\": 120.0\n        }\n    },\n    \"m_Slots\": [\n       
    {\n            \"m_Id\": \"03c85868ac4e48db9c626c2cd6f6d3a6\"\n        },\n       
    {\n            \"m_Id\": \"63ad72d5eeea4ff3bf050e038ce3eb6a\"\n        },\n       
    {\n            \"m_Id\": \"36d8d1c883fd49da9ce3de7bdaa45b28\"\n        }\n   
    ],\n    \"synonyms\": [\n        \"multiplication\",\n        \"times\",\n       
    \"x\"\n    ],\n    \"m_Precision\": 0,\n    \"m_PreviewExpanded\": false,\n   
    \"m_DismissedVersion\": 0,\n    \"m_PreviewMode\": 0,\n    \"m_CustomColors\":
    {\n        \"m_SerializableColors\": []\n    }\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.SimpleNoiseNode\",\n    \"m_ObjectId\":
    \"504f3b650b084582864b6fa1392cd5d3\",\n    \"m_Group\": {\n        \"m_Id\":
    \"\"\n    },\n    \"m_Name\": \"Simple Noise\",\n    \"m_DrawState\": {\n       
    \"m_Expanded\": true,\n        \"m_Position\": {\n            \"serializedVersion\":
    \"2\",\n            \"x\": -1650.0,\n            \"y\": 200.0,\n            \"width\":
    150.0,\n            \"height\": 105.0\n        }\n    },\n    \"m_Slots\": [\n       
    {\n            \"m_Id\": \"a875eabd6a0a450e84a7f6d05e8596eb\"\n        },\n       
    {\n            \"m_Id\": \"693b91efc4474a35b7cad5adf32d4547\"\n        },\n       
    {\n            \"m_Id\": \"65d0e82a6caf480db3ffc16e0d8fdc0b\"\n        }\n   
    ],\n    \"synonyms\": [\n        \"value noise\"\n    ],\n    \"m_Precision\":
    0,\n    \"m_PreviewExpanded\": false,\n    \"m_DismissedVersion\": 0,\n    \"m_PreviewMode\":
    0,\n    \"m_CustomColors\": {\n        \"m_SerializableColors\": []\n    },\n   
    \"m_HashType\": 0\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.AddNode\",\n   
    \"m_ObjectId\": \"9e31da9e949f4c0691e368688bc932ec\",\n    \"m_Group\": {\n       
    \"m_Id\": \"\"\n    },\n    \"m_Name\": \"Add\",\n    \"m_DrawState\": {\n       
    \"m_Expanded\": true,\n        \"m_Position\": {\n            \"serializedVersion\":
    \"2\",\n            \"x\": -1400.0,\n            \"y\": 300.0,\n            \"width\":
    130.0,\n            \"height\": 120.0\n        }\n    },\n    \"m_Slots\": [\n       
    {\n            \"m_Id\": \"c4ac6eb5351a4314aad390310029fb6e\"\n        },\n       
    {\n            \"m_Id\": \"aa6ac151106e405b9a9b406a24b55447\"\n        },\n       
    {\n            \"m_Id\": \"419f90368705441da9eee914bfe54401\"\n        }\n   
    ],\n    \"synonyms\": [\n        \"addition\",\n        \"sum\",\n        \"plus\"\n   
    ],\n    \"m_Precision\": 0,\n    \"m_PreviewExpanded\": false,\n    \"m_DismissedVersion\":
    0,\n    \"m_PreviewMode\": 0,\n    \"m_CustomColors\": {\n        \"m_SerializableColors\":
    []\n    }\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.AddNode\",\n   
    \"m_ObjectId\": \"3cdde6db00cf4017b29f25283f6e3600\",\n    \"m_Group\": {\n       
    \"m_Id\": \"\"\n    },\n    \"m_Name\": \"Add\",\n    \"m_DrawState\": {\n       
    \"m_Expanded\": true,\n        \"m_Position\": {\n            \"serializedVersion\":
    \"2\",\n            \"x\": -1650.0,\n            \"y\": 400.0,\n            \"width\":
    130.0,\n            \"height\": 120.0\n        }\n    },\n    \"m_Slots\": [\n       
    {\n            \"m_Id\": \"4840ef8ae3eb4a609eed8606a6420d4c\"\n        },\n       
    {\n            \"m_Id\": \"254c695cf5c64cd989475a5bb370a5a4\"\n        },\n       
    {\n            \"m_Id\": \"e61f81a512dd41318031706b70d385c0\"\n        }\n   
    ],\n    \"synonyms\": [\n        \"addition\",\n        \"sum\",\n        \"plus\"\n   
    ],\n    \"m_Precision\": 0,\n    \"m_PreviewExpanded\": false,\n    \"m_DismissedVersion\":
    0,\n    \"m_PreviewMode\": 0,\n    \"m_CustomColors\": {\n        \"m_SerializableColors\":
    []\n    }\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.SmoothstepNode\",\n   
    \"m_ObjectId\": \"67338e132b0944f481cbe668675fc9e3\",\n    \"m_Group\": {\n       
    \"m_Id\": \"\"\n    },\n    \"m_Name\": \"Smoothstep\",\n    \"m_DrawState\":
    {\n        \"m_Expanded\": true,\n        \"m_Position\": {\n            \"serializedVersion\":
    \"2\",\n            \"x\": -1100.0,\n            \"y\": 300.0,\n            \"width\":
    155.0,\n            \"height\": 145.0\n        }\n    },\n    \"m_Slots\": [\n       
    {\n            \"m_Id\": \"52eab82e70194471b6a595dd480f9fa2\"\n        },\n       
    {\n            \"m_Id\": \"f3abe9a30aa7486bbc86433d08c2cd71\"\n        },\n       
    {\n            \"m_Id\": \"6d9b1f01ae804bd7bc54c6676d7d489b\"\n        },\n       
    {\n            \"m_Id\": \"14ba59222a5e4871af53cd60c0244943\"\n        }\n   
    ],\n    \"synonyms\": [\n        \"curve\"\n    ],\n    \"m_Precision\": 0,\n   
    \"m_PreviewExpanded\": false,\n    \"m_DismissedVersion\": 0,\n    \"m_PreviewMode\":
    0,\n    \"m_CustomColors\": {\n        \"m_SerializableColors\": []\n    }\n}\n\n{\n   
    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.LerpNode\",\n   
    \"m_ObjectId\": \"d5c2e31468114a27bdbf49a716f00d7f\",\n    \"m_Group\": {\n       
    \"m_Id\": \"\"\n    },\n    \"m_Name\": \"Lerp\",\n    \"m_DrawState\": {\n       
    \"m_Expanded\": true,\n        \"m_Position\": {\n            \"serializedVersion\":
    \"2\",\n            \"x\": -750.0,\n            \"y\": 50.0,\n            \"width\":
    130.0,\n            \"height\": 145.0\n        }\n    },\n    \"m_Slots\": [\n       
    {\n            \"m_Id\": \"28c57b872a5247878be9c47a6747c331\"\n        },\n       
    {\n            \"m_Id\": \"88c33d1226b442ef9287539a1b288695\"\n        },\n       
    {\n            \"m_Id\": \"f36e50d6081b498c85b4bbddef544307\"\n        },\n       
    {\n            \"m_Id\": \"eac7e3c6683a4665b35bcbe5facb7aa2\"\n        }\n   
    ],\n    \"synonyms\": [\n        \"mix\",\n        \"blend\",\n        \"linear
    interpolate\"\n    ],\n    \"m_Precision\": 0,\n    \"m_PreviewExpanded\": false,\n   
    \"m_DismissedVersion\": 0,\n    \"m_PreviewMode\": 0,\n    \"m_CustomColors\":
    {\n        \"m_SerializableColors\": []\n    }\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.PropertyNode\",\n    \"m_ObjectId\":
    \"595b5bb08eb4494380c15eb7231482b3\",\n    \"m_Group\": {\n        \"m_Id\":
    \"\"\n    },\n    \"m_Name\": \"Property\",\n    \"m_DrawState\": {\n       
    \"m_Expanded\": true,\n        \"m_Position\": {\n            \"serializedVersion\":
    \"2\",\n            \"x\": -1300.0,\n            \"y\": 250.0,\n            \"width\":
    150.0,\n            \"height\": 35.0\n        }\n    },\n    \"m_Slots\": [\n       
    {\n            \"m_Id\": \"5db9cc287aa245b2bc3597becf98052e\"\n        }\n   
    ],\n    \"synonyms\": [],\n    \"m_Precision\": 0,\n    \"m_PreviewExpanded\":
    true,\n    \"m_DismissedVersion\": 0,\n    \"m_PreviewMode\": 0,\n    \"m_CustomColors\":
    {\n        \"m_SerializableColors\": []\n    },\n    \"m_Property\": {\n       
    \"m_Id\": \"39640db60ba8486caa16e6de768e9a15\"\n    }\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.PropertyNode\",\n    \"m_ObjectId\":
    \"d3aa7700a1fa4a7eab32325ed6bf8b37\",\n    \"m_Group\": {\n        \"m_Id\":
    \"\"\n    },\n    \"m_Name\": \"Property\",\n    \"m_DrawState\": {\n       
    \"m_Expanded\": true,\n        \"m_Position\": {\n            \"serializedVersion\":
    \"2\",\n            \"x\": -950.0,\n            \"y\": -50.0,\n            \"width\":
    150.0,\n            \"height\": 35.0\n        }\n    },\n    \"m_Slots\": [\n       
    {\n            \"m_Id\": \"48f3a8ac30274616978a33b7180b4b81\"\n        }\n   
    ],\n    \"synonyms\": [],\n    \"m_Precision\": 0,\n    \"m_PreviewExpanded\":
    true,\n    \"m_DismissedVersion\": 0,\n    \"m_PreviewMode\": 0,\n    \"m_CustomColors\":
    {\n        \"m_SerializableColors\": []\n    },\n    \"m_Property\": {\n       
    \"m_Id\": \"8de610096d3d46caa3fa4c411485ca63\"\n    }\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.PropertyNode\",\n    \"m_ObjectId\":
    \"ac3520b082ef413b8ab5954817163874\",\n    \"m_Group\": {\n        \"m_Id\":
    \"\"\n    },\n    \"m_Name\": \"Property\",\n    \"m_DrawState\": {\n       
    \"m_Expanded\": true,\n        \"m_Position\": {\n            \"serializedVersion\":
    \"2\",\n            \"x\": -950.0,\n            \"y\": 10.0,\n            \"width\":
    150.0,\n            \"height\": 35.0\n        }\n    },\n    \"m_Slots\": [\n       
    {\n            \"m_Id\": \"fd7de7f68e0749a185507465c5f17f8c\"\n        }\n   
    ],\n    \"synonyms\": [],\n    \"m_Precision\": 0,\n    \"m_PreviewExpanded\":
    true,\n    \"m_DismissedVersion\": 0,\n    \"m_PreviewMode\": 0,\n    \"m_CustomColors\":
    {\n        \"m_SerializableColors\": []\n    },\n    \"m_Property\": {\n       
    \"m_Id\": \"e62d302b15a84324bd038795690c6c4f\"\n    }\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.PropertyNode\",\n    \"m_ObjectId\":
    \"b8616360f4604ae080f5ef8ddd7af920\",\n    \"m_Group\": {\n        \"m_Id\":
    \"\"\n    },\n    \"m_Name\": \"Property\",\n    \"m_DrawState\": {\n       
    \"m_Expanded\": true,\n        \"m_Position\": {\n            \"serializedVersion\":
    \"2\",\n            \"x\": -2200.0,\n            \"y\": 600.0,\n            \"width\":
    150.0,\n            \"height\": 35.0\n        }\n    },\n    \"m_Slots\": [\n       
    {\n            \"m_Id\": \"d3530734fb734d258c9078cac8e58888\"\n        }\n   
    ],\n    \"synonyms\": [],\n    \"m_Precision\": 0,\n    \"m_PreviewExpanded\":
    true,\n    \"m_DismissedVersion\": 0,\n    \"m_PreviewMode\": 0,\n    \"m_CustomColors\":
    {\n        \"m_SerializableColors\": []\n    },\n    \"m_Property\": {\n       
    \"m_Id\": \"4e659e00b03444d6b6b50065b170dee2\"\n    }\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.BlockNode\",\n    \"m_ObjectId\":
    \"41275c9030fa4cc9a20b05bfb03cb7a0\",\n    \"m_Group\": {\n        \"m_Id\":
    \"\"\n    },\n    \"m_Name\": \"SurfaceDescription.BaseColor\",\n    \"m_DrawState\":
    {\n        \"m_Expanded\": true,\n        \"m_Position\": {\n            \"serializedVersion\":
    \"2\",\n            \"x\": 0.0,\n            \"y\": 0.0,\n            \"width\":
    0.0,\n            \"height\": 0.0\n        }\n    },\n    \"m_Slots\": [\n       
    {\n            \"m_Id\": \"99e693c7e34f4ea98038ec9827beaa0f\"\n        }\n   
    ],\n    \"synonyms\": [],\n    \"m_Precision\": 0,\n    \"m_PreviewExpanded\":
    true,\n    \"m_DismissedVersion\": 0,\n    \"m_PreviewMode\": 0,\n    \"m_CustomColors\":
    {\n        \"m_SerializableColors\": []\n    },\n    \"m_SerializedDescriptor\":
    \"SurfaceDescription.BaseColor\"\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\":
    \"UnityEditor.ShaderGraph.BlockNode\",\n    \"m_ObjectId\": \"e8ee9a36bc9b45358b78d91c6cc14fe5\",\n   
    \"m_Group\": {\n        \"m_Id\": \"\"\n    },\n    \"m_Name\": \"SurfaceDescription.Alpha\",\n   
    \"m_DrawState\": {\n        \"m_Expanded\": true,\n        \"m_Position\": {\n           
    \"serializedVersion\": \"2\",\n            \"x\": 0.0,\n            \"y\": 0.0,\n           
    \"width\": 0.0,\n            \"height\": 0.0\n        }\n    },\n    \"m_Slots\":
    [\n        {\n            \"m_Id\": \"9e5ad822b98242fc8ad26e75bf47c251\"\n       
    }\n    ],\n    \"synonyms\": [],\n    \"m_Precision\": 0,\n    \"m_PreviewExpanded\":
    true,\n    \"m_DismissedVersion\": 0,\n    \"m_PreviewMode\": 0,\n    \"m_CustomColors\":
    {\n        \"m_SerializableColors\": []\n    },\n    \"m_SerializedDescriptor\":
    \"SurfaceDescription.Alpha\"\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\":
    \"UnityEditor.Rendering.HighDefinition.ShaderGraph.HDTarget\",\n    \"m_ObjectId\":
    \"8ea1929a43394e3a9d3644bd98b99880\",\n    \"m_ActiveSubTarget\": {\n       
    \"m_Id\": \"d994452c7fc34a529537dabc289d6546\"\n    },\n    \"m_Datas\": [\n       
    {\n            \"m_Id\": \"e8483610d3df40f19681b1e861a8c936\"\n        },\n       
    {\n            \"m_Id\": \"40e0a1d78e634c35a5673ceb250d759b\"\n        },\n       
    {\n            \"m_Id\": \"36b5bede00d04024a70ab055b1c5bac7\"\n        }\n   
    ],\n    \"m_CustomEditorGUI\": \"\",\n    \"m_SupportVFX\": false,\n    \"m_SupportComputeForVertexSetup\":
    false\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.Rendering.HighDefinition.ShaderGraph.FogVolumeSubTarget\",\n   
    \"m_ObjectId\": \"d994452c7fc34a529537dabc289d6546\"\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.Rendering.HighDefinition.ShaderGraph.BuiltinData\",\n   
    \"m_ObjectId\": \"e8483610d3df40f19681b1e861a8c936\",\n    \"m_Distortion\":
    false,\n    \"m_DistortionMode\": 0,\n    \"m_DistortionDepthTest\": true,\n   
    \"m_AddPrecomputedVelocity\": false,\n    \"m_TransparentWritesMotionVec\": false,\n   
    \"m_DepthOffset\": false,\n    \"m_ConservativeDepthOffset\": false,\n    \"m_TransparencyFog\":
    true,\n    \"m_AlphaTestShadow\": false,\n    \"m_BackThenFrontRendering\": false,\n   
    \"m_TransparentDepthPrepass\": false,\n    \"m_TransparentDepthPostpass\": false,\n   
    \"m_SupportLodCrossFade\": false\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\":
    \"UnityEditor.Rendering.HighDefinition.ShaderGraph.SystemData\",\n    \"m_ObjectId\":
    \"40e0a1d78e634c35a5673ceb250d759b\",\n    \"m_MaterialNeedsUpdateHash\": 0,\n   
    \"m_SurfaceType\": 0,\n    \"m_RenderingPass\": 1,\n    \"m_BlendMode\": 0,\n   
    \"m_ZTest\": 4,\n    \"m_ZWrite\": false,\n    \"m_TransparentCullMode\": 2,\n   
    \"m_OpaqueCullMode\": 2,\n    \"m_SortPriority\": 0,\n    \"m_AlphaTest\": false,\n   
    \"m_TransparentDepthPrepass\": false,\n    \"m_TransparentDepthPostpass\": false,\n   
    \"m_SupportLodCrossFade\": false,\n    \"m_DoubleSidedMode\": 0,\n    \"m_DOTSInstancing\":
    false,\n    \"m_CustomVelocity\": false,\n    \"m_Tessellation\": false,\n   
    \"m_TessellationMode\": 0,\n    \"m_TessellationFactorMinDistance\": 20.0,\n   
    \"m_TessellationFactorMaxDistance\": 50.0,\n    \"m_TessellationFactorTriangleSize\":
    100.0,\n    \"m_TessellationShapeFactor\": 0.75,\n    \"m_TessellationBackFaceCullEpsilon\":
    -0.25,\n    \"m_TessellationMaxDisplacement\": 0.009999999776482582,\n    \"m_Version\":
    1,\n    \"inspectorFoldoutMask\": 0\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\":
    \"UnityEditor.Rendering.HighDefinition.ShaderGraph.FogVolumeData\",\n    \"m_ObjectId\":
    \"36b5bede00d04024a70ab055b1c5bac7\",\n    \"m_BlendMode\": 1\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.CategoryData\",\n    \"m_ObjectId\":
    \"b60bd87d1f314dbc8ba2a76f2f91acec\",\n    \"m_Name\": \"\",\n    \"m_ChildObjectList\":
    [\n        {\n            \"m_Id\": \"39640db60ba8486caa16e6de768e9a15\"\n       
    },\n        {\n            \"m_Id\": \"8de610096d3d46caa3fa4c411485ca63\"\n       
    },\n        {\n            \"m_Id\": \"e62d302b15a84324bd038795690c6c4f\"\n       
    },\n        {\n            \"m_Id\": \"d5c36d12d27240f7ab75d640fafab609\"\n       
    },\n        {\n            \"m_Id\": \"4e659e00b03444d6b6b50065b170dee2\"\n       
    }\n    ]\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.Vector4MaterialSlot\",\n   
    \"m_ObjectId\": \"d4bd3f378f2c4685a81cfa19793dc0f7\",\n    \"m_Id\": 0,\n   
    \"m_DisplayName\": \"Out\",\n    \"m_SlotType\": 1,\n    \"m_Hidden\": false,\n   
    \"m_ShaderOutputName\": \"Out\",\n    \"m_StageCapability\": 3,\n    \"m_Value\":
    {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\": 0.0,\n        \"w\":
    0.0\n    },\n    \"m_DefaultValue\": {\n        \"x\": 0.0,\n        \"y\": 0.0,\n       
    \"z\": 0.0,\n        \"w\": 0.0\n    },\n    \"m_Labels\": []\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.Vector1MaterialSlot\",\n    \"m_ObjectId\":
    \"b24e008a74a448cb84ed09d34f689359\",\n    \"m_Id\": 0,\n    \"m_DisplayName\":
    \"Time\",\n    \"m_SlotType\": 1,\n    \"m_Hidden\": false,\n    \"m_ShaderOutputName\":
    \"Time\",\n    \"m_StageCapability\": 3,\n    \"m_Value\": 0.0,\n    \"m_DefaultValue\":
    0.0,\n    \"m_Labels\": []\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\":
    \"UnityEditor.ShaderGraph.Vector1MaterialSlot\",\n    \"m_ObjectId\": \"76516320ee8a4f81956f5a30d51e962c\",\n   
    \"m_Id\": 1,\n    \"m_DisplayName\": \"Sine Time\",\n    \"m_SlotType\": 1,\n   
    \"m_Hidden\": false,\n    \"m_ShaderOutputName\": \"Sine Time\",\n    \"m_StageCapability\":
    3,\n    \"m_Value\": 0.0,\n    \"m_DefaultValue\": 0.0,\n    \"m_Labels\": []\n}\n\n{\n   
    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.Vector1MaterialSlot\",\n   
    \"m_ObjectId\": \"88ea5118b5b54c95a61ef9a95276b3de\",\n    \"m_Id\": 2,\n   
    \"m_DisplayName\": \"Cosine Time\",\n    \"m_SlotType\": 1,\n    \"m_Hidden\":
    false,\n    \"m_ShaderOutputName\": \"Cosine Time\",\n    \"m_StageCapability\":
    3,\n    \"m_Value\": 0.0,\n    \"m_DefaultValue\": 0.0,\n    \"m_Labels\": []\n}\n\n{\n   
    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.Vector1MaterialSlot\",\n   
    \"m_ObjectId\": \"fa2095111b464fd39674131453b6be0d\",\n    \"m_Id\": 3,\n   
    \"m_DisplayName\": \"Delta Time\",\n    \"m_SlotType\": 1,\n    \"m_Hidden\":
    false,\n    \"m_ShaderOutputName\": \"Delta Time\",\n    \"m_StageCapability\":
    3,\n    \"m_Value\": 0.0,\n    \"m_DefaultValue\": 0.0,\n    \"m_Labels\": []\n}\n\n{\n   
    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.Vector1MaterialSlot\",\n   
    \"m_ObjectId\": \"129d5c7fadc7433abad14b758a90a748\",\n    \"m_Id\": 4,\n   
    \"m_DisplayName\": \"Smooth Delta\",\n    \"m_SlotType\": 1,\n    \"m_Hidden\":
    false,\n    \"m_ShaderOutputName\": \"Smooth Delta\",\n    \"m_StageCapability\":
    3,\n    \"m_Value\": 0.0,\n    \"m_DefaultValue\": 0.0,\n    \"m_Labels\": []\n}\n\n{\n   
    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.DynamicValueMaterialSlot\",\n   
    \"m_ObjectId\": \"03c85868ac4e48db9c626c2cd6f6d3a6\",\n    \"m_Id\": 0,\n   
    \"m_DisplayName\": \"A\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\": false,\n   
    \"m_ShaderOutputName\": \"A\",\n    \"m_StageCapability\": 3,\n    \"m_Value\":
    {\n        \"e00\": 0.0,\n        \"e01\": 0.0,\n        \"e02\": 0.0,\n       
    \"e03\": 0.0,\n        \"e10\": 0.0,\n        \"e11\": 0.0,\n        \"e12\":
    0.0,\n        \"e13\": 0.0,\n        \"e20\": 0.0,\n        \"e21\": 0.0,\n       
    \"e22\": 0.0,\n        \"e23\": 0.0,\n        \"e30\": 0.0,\n        \"e31\":
    0.0,\n        \"e32\": 0.0,\n        \"e33\": 0.0\n    },\n    \"m_DefaultValue\":
    {\n        \"e00\": 1.0,\n        \"e01\": 0.0,\n        \"e02\": 0.0,\n       
    \"e03\": 0.0,\n        \"e10\": 0.0,\n        \"e11\": 1.0,\n        \"e12\":
    0.0,\n        \"e13\": 0.0,\n        \"e20\": 0.0,\n        \"e21\": 0.0,\n       
    \"e22\": 1.0,\n        \"e23\": 0.0,\n        \"e30\": 0.0,\n        \"e31\":
    0.0,\n        \"e32\": 0.0,\n        \"e33\": 1.0\n    }\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.DynamicValueMaterialSlot\",\n   
    \"m_ObjectId\": \"63ad72d5eeea4ff3bf050e038ce3eb6a\",\n    \"m_Id\": 1,\n   
    \"m_DisplayName\": \"B\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\": false,\n   
    \"m_ShaderOutputName\": \"B\",\n    \"m_StageCapability\": 3,\n    \"m_Value\":
    {\n        \"e00\": 2.0,\n        \"e01\": 2.0,\n        \"e02\": 2.0,\n       
    \"e03\": 2.0,\n        \"e10\": 2.0,\n        \"e11\": 2.0,\n        \"e12\":
    2.0,\n        \"e13\": 2.0,\n        \"e20\": 2.0,\n        \"e21\": 2.0,\n       
    \"e22\": 2.0,\n        \"e23\": 2.0,\n        \"e30\": 2.0,\n        \"e31\":
    2.0,\n        \"e32\": 2.0,\n        \"e33\": 2.0\n    },\n    \"m_DefaultValue\":
    {\n        \"e00\": 1.0,\n        \"e01\": 0.0,\n        \"e02\": 0.0,\n       
    \"e03\": 0.0,\n        \"e10\": 0.0,\n        \"e11\": 1.0,\n        \"e12\":
    0.0,\n        \"e13\": 0.0,\n        \"e20\": 0.0,\n        \"e21\": 0.0,\n       
    \"e22\": 1.0,\n        \"e23\": 0.0,\n        \"e30\": 0.0,\n        \"e31\":
    0.0,\n        \"e32\": 0.0,\n        \"e33\": 1.0\n    }\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.DynamicValueMaterialSlot\",\n   
    \"m_ObjectId\": \"36d8d1c883fd49da9ce3de7bdaa45b28\",\n    \"m_Id\": 2,\n   
    \"m_DisplayName\": \"Out\",\n    \"m_SlotType\": 1,\n    \"m_Hidden\": false,\n   
    \"m_ShaderOutputName\": \"Out\",\n    \"m_StageCapability\": 3,\n    \"m_Value\":
    {\n        \"e00\": 0.0,\n        \"e01\": 0.0,\n        \"e02\": 0.0,\n       
    \"e03\": 0.0,\n        \"e10\": 0.0,\n        \"e11\": 0.0,\n        \"e12\":
    0.0,\n        \"e13\": 0.0,\n        \"e20\": 0.0,\n        \"e21\": 0.0,\n       
    \"e22\": 0.0,\n        \"e23\": 0.0,\n        \"e30\": 0.0,\n        \"e31\":
    0.0,\n        \"e32\": 0.0,\n        \"e33\": 0.0\n    },\n    \"m_DefaultValue\":
    {\n        \"e00\": 1.0,\n        \"e01\": 0.0,\n        \"e02\": 0.0,\n       
    \"e03\": 0.0,\n        \"e10\": 0.0,\n        \"e11\": 1.0,\n        \"e12\":
    0.0,\n        \"e13\": 0.0,\n        \"e20\": 0.0,\n        \"e21\": 0.0,\n       
    \"e22\": 1.0,\n        \"e23\": 0.0,\n        \"e30\": 0.0,\n        \"e31\":
    0.0,\n        \"e32\": 0.0,\n        \"e33\": 1.0\n    }\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.DynamicVectorMaterialSlot\",\n   
    \"m_ObjectId\": \"a875eabd6a0a450e84a7f6d05e8596eb\",\n    \"m_Id\": 0,\n   
    \"m_DisplayName\": \"UV\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\": false,\n   
    \"m_ShaderOutputName\": \"UV\",\n    \"m_StageCapability\": 3,\n    \"m_Value\":
    {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\": 0.0,\n        \"w\":
    0.0\n    },\n    \"m_DefaultValue\": {\n        \"x\": 0.0,\n        \"y\": 0.0,\n       
    \"z\": 0.0,\n        \"w\": 0.0\n    }\n}\n\n{\n    \"m_SGVersion\": 0,\n   
    \"m_Type\": \"UnityEditor.ShaderGraph.Vector1MaterialSlot\",\n    \"m_ObjectId\":
    \"693b91efc4474a35b7cad5adf32d4547\",\n    \"m_Id\": 1,\n    \"m_DisplayName\":
    \"Scale\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\": false,\n    \"m_ShaderOutputName\":
    \"Scale\",\n    \"m_StageCapability\": 3,\n    \"m_Value\": 500.0,\n    \"m_DefaultValue\":
    500.0,\n    \"m_Labels\": []\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\":
    \"UnityEditor.ShaderGraph.Vector1MaterialSlot\",\n    \"m_ObjectId\": \"65d0e82a6caf480db3ffc16e0d8fdc0b\",\n   
    \"m_Id\": 2,\n    \"m_DisplayName\": \"Out\",\n    \"m_SlotType\": 1,\n    \"m_Hidden\":
    false,\n    \"m_ShaderOutputName\": \"Out\",\n    \"m_StageCapability\": 3,\n   
    \"m_Value\": 0.0,\n    \"m_DefaultValue\": 0.0,\n    \"m_Labels\": []\n}\n\n{\n   
    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.DynamicVectorMaterialSlot\",\n   
    \"m_ObjectId\": \"c4ac6eb5351a4314aad390310029fb6e\",\n    \"m_Id\": 0,\n   
    \"m_DisplayName\": \"A\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\": false,\n   
    \"m_ShaderOutputName\": \"A\",\n    \"m_StageCapability\": 3,\n    \"m_Value\":
    {\n        \"x\": 1.0,\n        \"y\": 1.0,\n        \"z\": 1.0,\n        \"w\":
    1.0\n    },\n    \"m_DefaultValue\": {\n        \"x\": 0.0,\n        \"y\": 0.0,\n       
    \"z\": 0.0,\n        \"w\": 0.0\n    }\n}\n\n{\n    \"m_SGVersion\": 0,\n   
    \"m_Type\": \"UnityEditor.ShaderGraph.DynamicVectorMaterialSlot\",\n    \"m_ObjectId\":
    \"aa6ac151106e405b9a9b406a24b55447\",\n    \"m_Id\": 1,\n    \"m_DisplayName\":
    \"B\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\": false,\n    \"m_ShaderOutputName\":
    \"B\",\n    \"m_StageCapability\": 3,\n    \"m_Value\": {\n        \"x\": 0.5,\n       
    \"y\": 0.5,\n        \"z\": 0.5,\n        \"w\": 0.0\n    },\n    \"m_DefaultValue\":
    {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\": 0.0,\n        \"w\":
    0.0\n    }\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.DynamicVectorMaterialSlot\",\n   
    \"m_ObjectId\": \"419f90368705441da9eee914bfe54401\",\n    \"m_Id\": 2,\n   
    \"m_DisplayName\": \"Out\",\n    \"m_SlotType\": 1,\n    \"m_Hidden\": false,\n   
    \"m_ShaderOutputName\": \"Out\",\n    \"m_StageCapability\": 3,\n    \"m_Value\":
    {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\": 0.0,\n        \"w\":
    0.0\n    },\n    \"m_DefaultValue\": {\n        \"x\": 0.0,\n        \"y\": 0.0,\n       
    \"z\": 0.0,\n        \"w\": 0.0\n    }\n}\n\n{\n    \"m_SGVersion\": 0,\n   
    \"m_Type\": \"UnityEditor.ShaderGraph.DynamicVectorMaterialSlot\",\n    \"m_ObjectId\":
    \"4840ef8ae3eb4a609eed8606a6420d4c\",\n    \"m_Id\": 0,\n    \"m_DisplayName\":
    \"A\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\": false,\n    \"m_ShaderOutputName\":
    \"A\",\n    \"m_StageCapability\": 3,\n    \"m_Value\": {\n        \"x\": 0.0,\n       
    \"y\": 0.0,\n        \"z\": 0.0,\n        \"w\": 0.0\n    },\n    \"m_DefaultValue\":
    {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\": 0.0,\n        \"w\":
    0.0\n    }\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.DynamicVectorMaterialSlot\",\n   
    \"m_ObjectId\": \"254c695cf5c64cd989475a5bb370a5a4\",\n    \"m_Id\": 1,\n   
    \"m_DisplayName\": \"B\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\": false,\n   
    \"m_ShaderOutputName\": \"B\",\n    \"m_StageCapability\": 3,\n    \"m_Value\":
    {\n        \"x\": 0.0,\n        \"y\": 0.25,\n        \"z\": 0.0,\n        \"w\":
    0.0\n    },\n    \"m_DefaultValue\": {\n        \"x\": 0.0,\n        \"y\": 0.0,\n       
    \"z\": 0.0,\n        \"w\": 0.0\n    }\n}\n\n{\n    \"m_SGVersion\": 0,\n   
    \"m_Type\": \"UnityEditor.ShaderGraph.DynamicVectorMaterialSlot\",\n    \"m_ObjectId\":
    \"e61f81a512dd41318031706b70d385c0\",\n    \"m_Id\": 2,\n    \"m_DisplayName\":
    \"Out\",\n    \"m_SlotType\": 1,\n    \"m_Hidden\": false,\n    \"m_ShaderOutputName\":
    \"Out\",\n    \"m_StageCapability\": 3,\n    \"m_Value\": {\n        \"x\": 0.0,\n       
    \"y\": 0.0,\n        \"z\": 0.0,\n        \"w\": 0.0\n    },\n    \"m_DefaultValue\":
    {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\": 0.0,\n        \"w\":
    0.0\n    }\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.DynamicVectorMaterialSlot\",\n   
    \"m_ObjectId\": \"52eab82e70194471b6a595dd480f9fa2\",\n    \"m_Id\": 0,\n   
    \"m_DisplayName\": \"Edge1\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\": false,\n   
    \"m_ShaderOutputName\": \"Edge1\",\n    \"m_StageCapability\": 3,\n    \"m_Value\":
    {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\": 0.0,\n        \"w\":
    0.0\n    },\n    \"m_DefaultValue\": {\n        \"x\": 0.0,\n        \"y\": 0.0,\n       
    \"z\": 0.0,\n        \"w\": 0.0\n    }\n}\n\n{\n    \"m_SGVersion\": 0,\n   
    \"m_Type\": \"UnityEditor.ShaderGraph.DynamicVectorMaterialSlot\",\n    \"m_ObjectId\":
    \"f3abe9a30aa7486bbc86433d08c2cd71\",\n    \"m_Id\": 1,\n    \"m_DisplayName\":
    \"Edge2\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\": false,\n    \"m_ShaderOutputName\":
    \"Edge2\",\n    \"m_StageCapability\": 3,\n    \"m_Value\": {\n        \"x\":
    1.0,\n        \"y\": 1.0,\n        \"z\": 1.0,\n        \"w\": 1.0\n    },\n   
    \"m_DefaultValue\": {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\":
    0.0,\n        \"w\": 0.0\n    }\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\":
    \"UnityEditor.ShaderGraph.DynamicVectorMaterialSlot\",\n    \"m_ObjectId\": \"6d9b1f01ae804bd7bc54c6676d7d489b\",\n   
    \"m_Id\": 2,\n    \"m_DisplayName\": \"In\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\":
    false,\n    \"m_ShaderOutputName\": \"In\",\n    \"m_StageCapability\": 3,\n   
    \"m_Value\": {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\": 0.0,\n       
    \"w\": 0.0\n    },\n    \"m_DefaultValue\": {\n        \"x\": 0.0,\n        \"y\":
    0.0,\n        \"z\": 0.0,\n        \"w\": 0.0\n    }\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.DynamicVectorMaterialSlot\",\n   
    \"m_ObjectId\": \"14ba59222a5e4871af53cd60c0244943\",\n    \"m_Id\": 3,\n   
    \"m_DisplayName\": \"Out\",\n    \"m_SlotType\": 1,\n    \"m_Hidden\": false,\n   
    \"m_ShaderOutputName\": \"Out\",\n    \"m_StageCapability\": 3,\n    \"m_Value\":
    {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\": 0.0,\n        \"w\":
    0.0\n    },\n    \"m_DefaultValue\": {\n        \"x\": 0.0,\n        \"y\": 0.0,\n       
    \"z\": 0.0,\n        \"w\": 0.0\n    }\n}\n\n{\n    \"m_SGVersion\": 0,\n   
    \"m_Type\": \"UnityEditor.ShaderGraph.DynamicVectorMaterialSlot\",\n    \"m_ObjectId\":
    \"28c57b872a5247878be9c47a6747c331\",\n    \"m_Id\": 0,\n    \"m_DisplayName\":
    \"A\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\": false,\n    \"m_ShaderOutputName\":
    \"A\",\n    \"m_StageCapability\": 3,\n    \"m_Value\": {\n        \"x\": 0.0,\n       
    \"y\": 0.0,\n        \"z\": 0.0,\n        \"w\": 0.0\n    },\n    \"m_DefaultValue\":
    {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\": 0.0,\n        \"w\":
    0.0\n    }\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.DynamicVectorMaterialSlot\",\n   
    \"m_ObjectId\": \"88c33d1226b442ef9287539a1b288695\",\n    \"m_Id\": 1,\n   
    \"m_DisplayName\": \"B\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\": false,\n   
    \"m_ShaderOutputName\": \"B\",\n    \"m_StageCapability\": 3,\n    \"m_Value\":
    {\n        \"x\": 1.0,\n        \"y\": 1.0,\n        \"z\": 1.0,\n        \"w\":
    1.0\n    },\n    \"m_DefaultValue\": {\n        \"x\": 0.0,\n        \"y\": 0.0,\n       
    \"z\": 0.0,\n        \"w\": 0.0\n    }\n}\n\n{\n    \"m_SGVersion\": 0,\n   
    \"m_Type\": \"UnityEditor.ShaderGraph.DynamicVectorMaterialSlot\",\n    \"m_ObjectId\":
    \"f36e50d6081b498c85b4bbddef544307\",\n    \"m_Id\": 2,\n    \"m_DisplayName\":
    \"T\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\": false,\n    \"m_ShaderOutputName\":
    \"T\",\n    \"m_StageCapability\": 3,\n    \"m_Value\": {\n        \"x\": 0.0,\n       
    \"y\": 0.0,\n        \"z\": 0.0,\n        \"w\": 0.0\n    },\n    \"m_DefaultValue\":
    {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\": 0.0,\n        \"w\":
    0.0\n    }\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.DynamicVectorMaterialSlot\",\n   
    \"m_ObjectId\": \"eac7e3c6683a4665b35bcbe5facb7aa2\",\n    \"m_Id\": 3,\n   
    \"m_DisplayName\": \"Out\",\n    \"m_SlotType\": 1,\n    \"m_Hidden\": false,\n   
    \"m_ShaderOutputName\": \"Out\",\n    \"m_StageCapability\": 3,\n    \"m_Value\":
    {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\": 0.0,\n        \"w\":
    0.0\n    },\n    \"m_DefaultValue\": {\n        \"x\": 0.0,\n        \"y\": 0.0,\n       
    \"z\": 0.0,\n        \"w\": 0.0\n    }\n}\n\n{\n    \"m_SGVersion\": 0,\n   
    \"m_Type\": \"UnityEditor.ShaderGraph.Vector1MaterialSlot\",\n    \"m_ObjectId\":
    \"5db9cc287aa245b2bc3597becf98052e\",\n    \"m_Id\": 0,\n    \"m_DisplayName\":
    \"Fog Density\",\n    \"m_SlotType\": 1,\n    \"m_Hidden\": false,\n    \"m_ShaderOutputName\":
    \"Out\",\n    \"m_StageCapability\": 3,\n    \"m_Value\": 0.0,\n    \"m_DefaultValue\":
    0.0,\n    \"m_Labels\": []\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\":
    \"UnityEditor.ShaderGraph.Vector4MaterialSlot\",\n    \"m_ObjectId\": \"48f3a8ac30274616978a33b7180b4b81\",\n   
    \"m_Id\": 0,\n    \"m_DisplayName\": \"Fog Color Deep\",\n    \"m_SlotType\":
    1,\n    \"m_Hidden\": false,\n    \"m_ShaderOutputName\": \"Out\",\n    \"m_StageCapability\":
    3,\n    \"m_Value\": {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\":
    0.0,\n        \"w\": 0.0\n    },\n    \"m_DefaultValue\": {\n        \"x\": 0.0,\n       
    \"y\": 0.0,\n        \"z\": 0.0,\n        \"w\": 0.0\n    },\n    \"m_Labels\":
    []\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.Vector4MaterialSlot\",\n   
    \"m_ObjectId\": \"fd7de7f68e0749a185507465c5f17f8c\",\n    \"m_Id\": 0,\n   
    \"m_DisplayName\": \"Fog Color Light\",\n    \"m_SlotType\": 1,\n    \"m_Hidden\":
    false,\n    \"m_ShaderOutputName\": \"Out\",\n    \"m_StageCapability\": 3,\n   
    \"m_Value\": {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\": 0.0,\n       
    \"w\": 0.0\n    },\n    \"m_DefaultValue\": {\n        \"x\": 0.0,\n        \"y\":
    0.0,\n        \"z\": 0.0,\n        \"w\": 0.0\n    },\n    \"m_Labels\": []\n}\n\n{\n   
    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.Vector1MaterialSlot\",\n   
    \"m_ObjectId\": \"d3530734fb734d258c9078cac8e58888\",\n    \"m_Id\": 0,\n   
    \"m_DisplayName\": \"Flow Speed\",\n    \"m_SlotType\": 1,\n    \"m_Hidden\":
    false,\n    \"m_ShaderOutputName\": \"Out\",\n    \"m_StageCapability\": 3,\n   
    \"m_Value\": 0.0,\n    \"m_DefaultValue\": 0.0,\n    \"m_Labels\": []\n}\n\n{\n   
    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.ColorRGBMaterialSlot\",\n   
    \"m_ObjectId\": \"99e693c7e34f4ea98038ec9827beaa0f\",\n    \"m_Id\": 0,\n   
    \"m_DisplayName\": \"Base Color\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\":
    false,\n    \"m_ShaderOutputName\": \"BaseColor\",\n    \"m_StageCapability\":
    2,\n    \"m_Value\": {\n        \"x\": 1.0,\n        \"y\": 1.0,\n        \"z\":
    1.0\n    },\n    \"m_DefaultValue\": {\n        \"x\": 0.0,\n        \"y\": 0.0,\n       
    \"z\": 0.0\n    },\n    \"m_Labels\": [],\n    \"m_ColorMode\": 0,\n    \"m_DefaultColor\":
    {\n        \"r\": 0.5,\n        \"g\": 0.5,\n        \"b\": 0.5,\n        \"a\":
    1.0\n    }\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.Vector1MaterialSlot\",\n   
    \"m_ObjectId\": \"9e5ad822b98242fc8ad26e75bf47c251\",\n    \"m_Id\": 0,\n   
    \"m_DisplayName\": \"Alpha\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\": false,\n   
    \"m_ShaderOutputName\": \"Alpha\",\n    \"m_StageCapability\": 2,\n    \"m_Value\":
    1.0,\n    \"m_DefaultValue\": 1.0,\n    \"m_Labels\": []\n}"
  m_AssetMaybeChangedOnDisk: 1
  m_AssetMaybeDeleted: 0
--- !u!114 &17
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 12010, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Children:
  - {fileID: 18}
  - {fileID: 20}
  - {fileID: 22}
  m_Position:
    serializedVersion: 2
    x: 0
    y: 1025
    width: 1954
    height: 261
  m_MinSize: {x: 300, y: 50}
  m_MaxSize: {x: 24288, y: 8096}
  vertical: 0
  controlID: 43517
  draggingID: 0
--- !u!114 &18
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 1
  m_Script: {fileID: 12006, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: ProjectBrowser
  m_EditorClassIdentifier: 
  m_Children: []
  m_Position:
    serializedVersion: 2
    x: 0
    y: 0
    width: 355
    height: 261
  m_MinSize: {x: 231, y: 276}
  m_MaxSize: {x: 10001, y: 10026}
  m_ActualView: {fileID: 19}
  m_Panes:
  - {fileID: 19}
  m_Selected: 0
  m_LastSelected: 0
--- !u!114 &19
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 1
  m_Script: {fileID: 12014, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_MinSize: {x: 230, y: 250}
  m_MaxSize: {x: 10000, y: 10000}
  m_TitleContent:
    m_Text: Project
    m_Image: {fileID: -5467254957812901981, guid: 0000000000000000d000000000000000, type: 0}
    m_Tooltip: 
    m_TextWithWhitespace: "Project\u200B"
  m_Pos:
    serializedVersion: 2
    x: 633
    y: 1136
    width: 354
    height: 235
  m_SerializedDataModeController:
    m_DataMode: 0
    m_PreferredDataMode: 0
    m_SupportedDataModes: 
    isAutomatic: 1
  m_ViewDataDictionary: {fileID: 0}
  m_OverlayCanvas:
    m_LastAppliedPresetName: Default
    m_SaveData: []
    m_ContainerData: []
    m_OverlaysVisible: 1
  m_SearchFilter:
    m_NameFilter: 
    m_ClassNames: []
    m_AssetLabels: []
    m_AssetBundleNames: []
    m_ReferencingInstanceIDs: 
    m_SceneHandles: 
    m_ShowAllHits: 0
    m_SkipHidden: 1
    m_SearchArea: 1
    m_Folders:
    - Assets
    m_Globs: []
    m_ProductIds: 
    m_AnyWithAssetOrigin: 0
    m_OriginalText: 
    m_ImportLogFlags: 0
    m_FilterByTypeIntersection: 0
  m_ViewMode: 1
  m_StartGridSize: 16
  m_LastFolders:
  - Assets/_MCmodForCodePurposes
  m_LastFoldersGridSize: 16
  m_LastProjectPath: C:\Unity\BLAME\BLAME
  m_LockTracker:
    m_IsLocked: 1
  m_FolderTreeState:
    scrollPos: {x: 0, y: 101}
    m_SelectedIDs: 58fa0000
    m_LastClickedID: 64088
    m_ExpandedIDs: 0000000058fa00005afa00005cfa00005efa000060fa000062fa000064fa000066fa000068fa00006afa00006cfa00006efa000070fa000072fa000074fa000076fa000078fa00007afa00007cfa00007efa000080fa000082fa000084fa000086fa000088fa00008afa00008cfa000090fa000092fa000094fa000096fa000098fa00009afa00009cfa00009efa0000a0fa0000a2fa0000a4fa0000a6fa0000a8fa0000aafa0000acfa0000a4fb000008fc000000ca9a3b
    m_RenameOverlay:
      m_UserAcceptedRename: 0
      m_Name: 
      m_OriginalName: 
      m_EditFieldRect:
        serializedVersion: 2
        x: 0
        y: 0
        width: 0
        height: 0
      m_UserData: 0
      m_IsWaitingForDelay: 0
      m_IsRenaming: 0
      m_OriginalEventType: 11
      m_IsRenamingFilename: 1
      m_TrimLeadingAndTrailingWhitespace: 0
      m_ClientGUIView: {fileID: 18}
    m_SearchString: 
    m_CreateAssetUtility:
      m_EndAction: {fileID: 0}
      m_InstanceID: 0
      m_Path: 
      m_Icon: {fileID: 0}
      m_ResourceFile: 
  m_AssetTreeState:
    scrollPos: {x: 0, y: 0}
    m_SelectedIDs: 0ebd0000
    m_LastClickedID: 48398
    m_ExpandedIDs: 0000000058fa00005afa00005cfa00005efa000060fa000062fa000064fa000066fa000068fa00006afa00006cfa00006efa000070fa000072fa000074fa000076fa000078fa00007afa00007cfa00007efa000080fa000082fa000084fa000086fa000088fa00008afa00008cfa000090fa000092fa000094fa000096fa000098fa00009afa00009cfa00009efa0000a0fa0000a2fa0000a4fa0000a6fa0000a8fa0000aafa0000acfa0000a4fb000008fc000000ca9a3b
    m_RenameOverlay:
      m_UserAcceptedRename: 0
      m_Name: 
      m_OriginalName: 
      m_EditFieldRect:
        serializedVersion: 2
        x: 0
        y: 0
        width: 0
        height: 0
      m_UserData: 0
      m_IsWaitingForDelay: 0
      m_IsRenaming: 0
      m_OriginalEventType: 11
      m_IsRenamingFilename: 1
      m_TrimLeadingAndTrailingWhitespace: 0
      m_ClientGUIView: {fileID: 0}
    m_SearchString: 
    m_CreateAssetUtility:
      m_EndAction: {fileID: 0}
      m_InstanceID: 0
      m_Path: 
      m_Icon: {fileID: 0}
      m_ResourceFile: 
  m_ListAreaState:
    m_SelectedInstanceIDs: deb80000
    m_LastClickedInstanceID: 47326
    m_HadKeyboardFocusLastEvent: 1
    m_ExpandedInstanceIDs: c6230000f68f0000ea8f0000f08f000084e60000b6f100002ebd000002bd0000e2d000001ebe0000e4b80000cabe0000e6bc0000000000004cc60000f622010062ab0100f4f70000
    m_RenameOverlay:
      m_UserAcceptedRename: 0
      m_Name: 
      m_OriginalName: 
      m_EditFieldRect:
        serializedVersion: 2
        x: 0
        y: 0
        width: 0
        height: 0
      m_UserData: 0
      m_IsWaitingForDelay: 0
      m_IsRenaming: 0
      m_OriginalEventType: 11
      m_IsRenamingFilename: 1
      m_TrimLeadingAndTrailingWhitespace: 0
      m_ClientGUIView: {fileID: 18}
    m_CreateAssetUtility:
      m_EndAction: {fileID: 0}
      m_InstanceID: 0
      m_Path: 
      m_Icon: {fileID: 0}
      m_ResourceFile: 
    m_NewAssetIndexInList: -1
    m_ScrollPosition: {x: 0, y: 0}
    m_GridSize: 16
  m_SkipHiddenPackages: 1
  m_DirectoriesAreaWidth: 124
--- !u!114 &20
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 12006, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: ProjectBrowser
  m_EditorClassIdentifier: 
  m_Children: []
  m_Position:
    serializedVersion: 2
    x: 355
    y: 0
    width: 676
    height: 261
  m_MinSize: {x: 232, y: 276}
  m_MaxSize: {x: 10002, y: 10026}
  m_ActualView: {fileID: 21}
  m_Panes:
  - {fileID: 21}
  m_Selected: 0
  m_LastSelected: 0
--- !u!114 &21
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 12014, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_MinSize: {x: 230, y: 250}
  m_MaxSize: {x: 10000, y: 10000}
  m_TitleContent:
    m_Text: Project
    m_Image: {fileID: -5467254957812901981, guid: 0000000000000000d000000000000000, type: 0}
    m_Tooltip: 
    m_TextWithWhitespace: "Project\u200B"
  m_Pos:
    serializedVersion: 2
    x: 988
    y: 1136
    width: 674
    height: 235
  m_SerializedDataModeController:
    m_DataMode: 0
    m_PreferredDataMode: 0
    m_SupportedDataModes: 
    isAutomatic: 1
  m_ViewDataDictionary: {fileID: 0}
  m_OverlayCanvas:
    m_LastAppliedPresetName: Default
    m_SaveData: []
    m_ContainerData: []
    m_OverlaysVisible: 1
  m_SearchFilter:
    m_NameFilter: 
    m_ClassNames: []
    m_AssetLabels: []
    m_AssetBundleNames: []
    m_ReferencingInstanceIDs: 
    m_SceneHandles: 
    m_ShowAllHits: 0
    m_SkipHidden: 0
    m_SearchArea: 1
    m_Folders:
    - Assets/_Game/Scripts/Inv
    m_Globs: []
    m_ProductIds: 
    m_AnyWithAssetOrigin: 0
    m_OriginalText: 
    m_ImportLogFlags: 0
    m_FilterByTypeIntersection: 0
  m_ViewMode: 1
  m_StartGridSize: 16
  m_LastFolders:
  - Assets/_Game/Scripts/Inv
  m_LastFoldersGridSize: 16
  m_LastProjectPath: C:\Unity\BLAME\BLAME
  m_LockTracker:
    m_IsLocked: 0
  m_FolderTreeState:
    scrollPos: {x: 0, y: 393}
    m_SelectedIDs: ccfb0000
    m_LastClickedID: 64460
    m_ExpandedIDs: 0000000058fa00005afa00005cfa00005efa000060fa000062fa000064fa000066fa000068fa00006afa00006cfa00006efa000070fa000072fa000074fa000076fa000078fa00007afa00007cfa00007efa000080fa000082fa000084fa000086fa000088fa00008afa00008cfa00008efa000090fa000092fa000094fa000096fa000098fa00009afa00009cfa00009efa0000a0fa0000a2fa0000a4fa0000a6fa0000a8fa0000aafa0000acfa0000a4fb000008fc000000ca9a3b
    m_RenameOverlay:
      m_UserAcceptedRename: 0
      m_Name: 
      m_OriginalName: 
      m_EditFieldRect:
        serializedVersion: 2
        x: 0
        y: 0
        width: 0
        height: 0
      m_UserData: 0
      m_IsWaitingForDelay: 0
      m_IsRenaming: 0
      m_OriginalEventType: 11
      m_IsRenamingFilename: 1
      m_TrimLeadingAndTrailingWhitespace: 0
      m_ClientGUIView: {fileID: 20}
    m_SearchString: 
    m_CreateAssetUtility:
      m_EndAction: {fileID: 0}
      m_InstanceID: 0
      m_Path: 
      m_Icon: {fileID: 0}
      m_ResourceFile: 
  m_AssetTreeState:
    scrollPos: {x: 0, y: 0}
    m_SelectedIDs: 
    m_LastClickedID: 0
    m_ExpandedIDs: 0000000058fa00005afa00005cfa00005efa000060fa000062fa000064fa000066fa000068fa00006afa00006cfa00006efa000070fa000072fa000074fa000076fa000078fa00007afa00007cfa00007efa000080fa000082fa000084fa000086fa000088fa00008afa00008cfa000090fa000092fa000094fa000096fa000098fa00009afa00009cfa00009efa0000a0fa0000a2fa0000a4fa0000a6fa0000a8fa0000aafa0000acfa0000a4fb000008fc000000ca9a3b
    m_RenameOverlay:
      m_UserAcceptedRename: 0
      m_Name: 
      m_OriginalName: 
      m_EditFieldRect:
        serializedVersion: 2
        x: 0
        y: 0
        width: 0
        height: 0
      m_UserData: 0
      m_IsWaitingForDelay: 0
      m_IsRenaming: 0
      m_OriginalEventType: 11
      m_IsRenamingFilename: 1
      m_TrimLeadingAndTrailingWhitespace: 0
      m_ClientGUIView: {fileID: 0}
    m_SearchString: 
    m_CreateAssetUtility:
      m_EndAction: {fileID: 0}
      m_InstanceID: 0
      m_Path: 
      m_Icon: {fileID: 0}
      m_ResourceFile: 
  m_ListAreaState:
    m_SelectedInstanceIDs: deb80000
    m_LastClickedInstanceID: 47326
    m_HadKeyboardFocusLastEvent: 1
    m_ExpandedInstanceIDs: fab8000062ab010070e60000627703004ad600001cf1fdffaec9fdff7ed80000503e0300f6e8000042ed0000e2d40400b8040000000000003cfa00007ced15005cfa00007c9f0300
    m_RenameOverlay:
      m_UserAcceptedRename: 0
      m_Name: 
      m_OriginalName: 
      m_EditFieldRect:
        serializedVersion: 2
        x: 0
        y: 0
        width: 0
        height: 0
      m_UserData: 0
      m_IsWaitingForDelay: 0
      m_IsRenaming: 0
      m_OriginalEventType: 11
      m_IsRenamingFilename: 1
      m_TrimLeadingAndTrailingWhitespace: 0
      m_ClientGUIView: {fileID: 20}
    m_CreateAssetUtility:
      m_EndAction: {fileID: 0}
      m_InstanceID: 0
      m_Path: 
      m_Icon: {fileID: 0}
      m_ResourceFile: 
    m_NewAssetIndexInList: -1
    m_ScrollPosition: {x: 0, y: 178}
    m_GridSize: 16
  m_SkipHiddenPackages: 0
  m_DirectoriesAreaWidth: 213
--- !u!114 &22
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 12006, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: ConsoleWindow
  m_EditorClassIdentifier: 
  m_Children: []
  m_Position:
    serializedVersion: 2
    x: 1031
    y: 0
    width: 923
    height: 261
  m_MinSize: {x: 102, y: 126}
  m_MaxSize: {x: 4002, y: 4026}
  m_ActualView: {fileID: 23}
  m_Panes:
  - {fileID: 23}
  - {fileID: 24}
  m_Selected: 0
  m_LastSelected: 1
--- !u!114 &23
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 1
  m_Script: {fileID: 12003, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_MinSize: {x: 100, y: 100}
  m_MaxSize: {x: 4000, y: 4000}
  m_TitleContent:
    m_Text: Console
    m_Image: {fileID: -4327648978806127646, guid: 0000000000000000d000000000000000, type: 0}
    m_Tooltip: 
    m_TextWithWhitespace: "Console\u200B"
  m_Pos:
    serializedVersion: 2
    x: 1664
    y: 1136
    width: 921
    height: 235
  m_SerializedDataModeController:
    m_DataMode: 0
    m_PreferredDataMode: 0
    m_SupportedDataModes: 
    isAutomatic: 1
  m_ViewDataDictionary: {fileID: 0}
  m_OverlayCanvas:
    m_LastAppliedPresetName: Default
    m_SaveData: []
    m_ContainerData: []
    m_OverlaysVisible: 1
--- !u!114 &24
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7c66a740a74845a08dc18bab7f2ffa03, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_MinSize: {x: 560, y: 200}
  m_MaxSize: {x: 4000, y: 4000}
  m_TitleContent:
    m_Text: Recorder
    m_Image: {fileID: 0}
    m_Tooltip: 
    m_TextWithWhitespace: "Recorder\u200B"
  m_Pos:
    serializedVersion: 2
    x: 1955
    y: 1042
    width: 907
    height: 300
  m_SerializedDataModeController:
    m_DataMode: 0
    m_PreferredDataMode: 0
    m_SupportedDataModes: 
    isAutomatic: 1
  m_ViewDataDictionary: {fileID: 0}
  m_OverlayCanvas:
    m_LastAppliedPresetName: Default
    m_SaveData: []
    m_ContainerData: []
    m_OverlaysVisible: 1
--- !u!114 &25
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 1
  m_Script: {fileID: 12006, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: InspectorWindow
  m_EditorClassIdentifier: 
  m_Children: []
  m_Position:
    serializedVersion: 2
    x: 1954
    y: 0
    width: 452
    height: 1286
  m_MinSize: {x: 275, y: 50}
  m_MaxSize: {x: 4000, y: 4000}
  m_ActualView: {fileID: 26}
  m_Panes:
  - {fileID: 26}
  - {fileID: 27}
  - {fileID: 28}
  - {fileID: 29}
  - {fileID: 30}
  m_Selected: 0
  m_LastSelected: 1
--- !u!114 &26
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 1
  m_Script: {fileID: 12019, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_MinSize: {x: 275, y: 50}
  m_MaxSize: {x: 4000, y: 4000}
  m_TitleContent:
    m_Text: Inspector
    m_Image: {fileID: -2667387946076563598, guid: 0000000000000000d000000000000000, type: 0}
    m_Tooltip: 
    m_TextWithWhitespace: "Inspector\u200B"
  m_Pos:
    serializedVersion: 2
    x: 2587
    y: 111
    width: 451
    height: 1260
  m_SerializedDataModeController:
    m_DataMode: 0
    m_PreferredDataMode: 0
    m_SupportedDataModes: 
    isAutomatic: 1
  m_ViewDataDictionary: {fileID: 0}
  m_OverlayCanvas:
    m_LastAppliedPresetName: Default
    m_SaveData: []
    m_ContainerData: []
    m_OverlaysVisible: 1
  m_ObjectsLockedBeforeSerialization: []
  m_InstanceIDsLockedBeforeSerialization: 
  m_PreviewResizer:
    m_CachedPref: -151
    m_ControlHash: -371814159
    m_PrefName: Preview_InspectorPreview
  m_LastInspectedObjectInstanceID: -1
  m_LastVerticalScrollValue: 0
  m_GlobalObjectId: 
  m_InspectorMode: 0
  m_LockTracker:
    m_IsLocked: 0
  m_PreviewWindow: {fileID: 0}
--- !u!114 &27
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: dc64e635488f60747bf5e9025c593285, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_MinSize: {x: 250, y: 530}
  m_MaxSize: {x: 250, y: 531}
  m_TitleContent:
    m_Text: Bakery
    m_Image: {fileID: 2800000, guid: 9f3214aa72e84444bafd15f7a4c74fd5, type: 3}
    m_Tooltip: 
    m_TextWithWhitespace: "Bakery\u200B"
  m_Pos:
    serializedVersion: 2
    x: 2793
    y: 79
    width: 646
    height: 1315
  m_SerializedDataModeController:
    m_DataMode: 0
    m_PreferredDataMode: 0
    m_SupportedDataModes: 
    isAutomatic: 1
  m_ViewDataDictionary: {fileID: 0}
  m_OverlayCanvas:
    m_LastAppliedPresetName: Default
    m_SaveData: []
    m_ContainerData: []
    m_OverlaysVisible: 1
  giSamples: 5
  priority: 3
  texelsPerUnit: 5
  userRenderMode: 0
  settingsMode: 0
  exeMode: 1
  deferredMode: 1
  unloadScenesInDeferredMode: 1
  fixSeams: 1
  denoise: 1
  denoise2x: 0
  encode: 1
  padding: 16
  dilate: 16
  selectedOnly: 0
  lightProbeRenderSize: 128
  lightProbeReadSize: 16
  lightProbeMaxCoeffs: 9
  renderSettingsStorage: {fileID: 0}
--- !u!114 &28
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 12079, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_MinSize: {x: 390, y: 390}
  m_MaxSize: {x: 4000, y: 4000}
  m_TitleContent:
    m_Text: Lighting
    m_Image: {fileID: -1347227620855488341, guid: 0000000000000000d000000000000000, type: 0}
    m_Tooltip: 
    m_TextWithWhitespace: "Lighting\u200B"
  m_Pos:
    serializedVersion: 2
    x: 2793
    y: 79
    width: 646
    height: 1315
  m_SerializedDataModeController:
    m_DataMode: 0
    m_PreferredDataMode: 0
    m_SupportedDataModes: 
    isAutomatic: 1
  m_ViewDataDictionary: {fileID: 0}
  m_OverlayCanvas:
    m_LastAppliedPresetName: Default
    m_SaveData: []
    m_ContainerData: []
    m_OverlaysVisible: 1
--- !u!114 &29
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 12090, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_MinSize: {x: 300, y: 250}
  m_MaxSize: {x: 4000, y: 4000}
  m_TitleContent:
    m_Text: Occlusion
    m_Image: {fileID: -3571815596324412722, guid: 0000000000000000d000000000000000, type: 0}
    m_Tooltip: 
    m_TextWithWhitespace: "Occlusion\u200B"
  m_Pos:
    serializedVersion: 2
    x: 2677
    y: 87
    width: 762
    height: 1315
  m_SerializedDataModeController:
    m_DataMode: 0
    m_PreferredDataMode: 0
    m_SupportedDataModes: 
    isAutomatic: 1
  m_ViewDataDictionary: {fileID: 0}
  m_OverlayCanvas:
    m_LastAppliedPresetName: Default
    m_SaveData: []
    m_ContainerData: []
    m_OverlaysVisible: 1
--- !u!114 &30
MonoBehaviour:
  m_ObjectHideFlags: 61
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 41147144ff556e246b736135eb26f185, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_MinSize: {x: 50, y: 50}
  m_MaxSize: {x: 4000, y: 4000}
  m_TitleContent:
    m_Text: Rendering Debugger
    m_Image: {fileID: 0}
    m_Tooltip: 
    m_TextWithWhitespace: "Rendering Debugger\u200B"
  m_Pos:
    serializedVersion: 2
    x: 2793
    y: 79
    width: 646
    height: 1315
  m_SerializedDataModeController:
    m_DataMode: 0
    m_PreferredDataMode: 0
    m_SupportedDataModes: 
    isAutomatic: 1
  m_ViewDataDictionary: {fileID: 0}
  m_OverlayCanvas:
    m_LastAppliedPresetName: Default
    m_SaveData: []
    m_ContainerData: []
    m_OverlaysVisible: 1
  m_WidgetStates:
    m_Keys:
    - Scene Camera -> Rendering
    - Scene Camera -> Rendering -> Lit Shader Mode
    - Scene Camera -> Rendering -> Clear GBuffers
    - Scene Camera -> Rendering -> Full Depth Prepass within Deferred
    - Scene Camera -> Rendering -> Opaque Objects
    - Scene Camera -> Rendering -> Transparent Objects
    - Scene Camera -> Rendering -> Transparent Prepass
    - Scene Camera -> Rendering -> Transparent Postpass
    - Scene Camera -> Rendering -> Low Resolution Transparent
    - Scene Camera -> Rendering -> Refraction
    - Scene Camera -> Rendering -> Water
    - Scene Camera -> Rendering -> Water Exclusion
    - Scene Camera -> Rendering -> Water Decals
    - Scene Camera -> Rendering -> Compute Thickness
    - Scene Camera -> Rendering -> Decals
    - Scene Camera -> Rendering -> Decal Layers
    - Scene Camera -> Rendering -> Rendering Layer Mask Buffer
    - Scene Camera -> Rendering -> Ray Tracing
    - Scene Camera -> Rendering -> RaytracingVFX
    - Scene Camera -> Rendering -> Custom Pass
    - Scene Camera -> Rendering -> Variable Rate Shading
    - Scene Camera -> Rendering -> Motion Vectors
    - Scene Camera -> Rendering -> Opaque Object Motion
    - Scene Camera -> Rendering -> Transparent Object Motion
    - Scene Camera -> Rendering -> Distortion
    - Scene Camera -> Rendering -> Rough Distortion
    - Scene Camera -> Rendering -> Post-process
    - Scene Camera -> Rendering -> Custom Post-process
    - Scene Camera -> Rendering -> Stop NaN
    - Scene Camera -> Rendering -> Depth Of Field
    - Scene Camera -> Rendering -> Motion Blur
    - Scene Camera -> Rendering -> Panini Projection
    - Scene Camera -> Rendering -> Bloom
    - Scene Camera -> Rendering -> Screen Space Lens Flare
    - Scene Camera -> Rendering -> Lens Distortion
    - Scene Camera -> Rendering -> Chromatic Aberration
    - Scene Camera -> Rendering -> Vignette
    - Scene Camera -> Rendering -> Color Grading
    - Scene Camera -> Rendering -> Film Grain
    - Scene Camera -> Rendering -> Dithering
    - Scene Camera -> Rendering -> Anti-aliasing
    - Scene Camera -> Rendering -> Tonemapping
    - Scene Camera -> Rendering -> Lens Flare Data Driven
    - Scene Camera -> Rendering -> After Post-process
    - Scene Camera -> Rendering -> Depth Test
    - Scene Camera -> Rendering -> Virtual Texturing
    - Scene Camera -> Rendering -> Asymmetric Projection
    - Scene Camera -> Rendering -> Screen Coordinates Override
    - Scene Camera -> Rendering -> High Quality Line Rendering
    - Scene Camera -> Lighting
    - Scene Camera -> Lighting -> Shadow Maps
    - Scene Camera -> Lighting -> Contact Shadows
    - Scene Camera -> Lighting -> Adaptive Probe Volumes
    - Scene Camera -> Lighting -> Normalize Reflection Probes
    - Scene Camera -> Lighting -> Screen Space Shadows
    - Scene Camera -> Lighting -> Shadowmask
    - Scene Camera -> Lighting -> Screen Space Reflection
    - Scene Camera -> Lighting -> Transparents
    - Scene Camera -> Lighting -> Screen Space Global Illumination
    - Scene Camera -> Lighting -> Screen Space Ambient Occlusion
    - Scene Camera -> Lighting -> Transmission
    - Scene Camera -> Lighting -> Fog
    - Scene Camera -> Lighting -> Volumetric Fog
    - Scene Camera -> Lighting -> Reprojection
    - Scene Camera -> Lighting -> Light Layers
    - Scene Camera -> Lighting -> Exposure Control
    - Scene Camera -> Lighting -> Reflection Probe
    - Scene Camera -> Lighting -> Planar Reflection Probe
    - Scene Camera -> Lighting -> Metallic Indirect Fallback
    - Scene Camera -> Lighting -> Sky Reflection
    - Scene Camera -> Lighting -> Direct Specular Lighting
    - Scene Camera -> Lighting -> Subsurface Scattering
    - Scene Camera -> Lighting -> Volumetric Clouds
    - Scene Camera -> Lighting -> Full Resolution Clouds For Sky
    - Scene Camera -> Async Compute
    - Scene Camera -> Async Compute -> Asynchronous Execution
    - Scene Camera -> Async Compute -> Light List
    - Scene Camera -> Async Compute -> SS Reflection
    - Scene Camera -> Async Compute -> SS Ambient Occlusion
    - Scene Camera -> Async Compute -> Contact Shadows
    - Scene Camera -> Async Compute -> Volume Voxelizations
    - Scene Camera -> Async Compute -> High Quality Line Rendering
    - Scene Camera -> Light Loop
    - Scene Camera -> Light Loop -> FPTL For Forward Opaque
    - Scene Camera -> Light Loop -> Big Tile Prepass
    - Scene Camera -> Light Loop -> Compute Light Variants
    - Scene Camera -> Light Loop -> Compute Material Variants
    - RagCam -> Rendering
    - RagCam -> Rendering -> Lit Shader Mode
    - RagCam -> Rendering -> Clear GBuffers
    - RagCam -> Rendering -> Full Depth Prepass within Deferred
    - RagCam -> Rendering -> Opaque Objects
    - RagCam -> Rendering -> Transparent Objects
    - RagCam -> Rendering -> Transparent Prepass
    - RagCam -> Rendering -> Transparent Postpass
    - RagCam -> Rendering -> Low Resolution Transparent
    - RagCam -> Rendering -> Refraction
    - RagCam -> Rendering -> Water
    - RagCam -> Rendering -> Water Exclusion
    - RagCam -> Rendering -> Water Decals
    - RagCam -> Rendering -> Compute Thickness
    - RagCam -> Rendering -> Decals
    - RagCam -> Rendering -> Decal Layers
    - RagCam -> Rendering -> Rendering Layer Mask Buffer
    - RagCam -> Rendering -> Ray Tracing
    - RagCam -> Rendering -> RaytracingVFX
    - RagCam -> Rendering -> Custom Pass
    - RagCam -> Rendering -> Variable Rate Shading
    - RagCam -> Rendering -> Motion Vectors
    - RagCam -> Rendering -> Opaque Object Motion
    - RagCam -> Rendering -> Transparent Object Motion
    - RagCam -> Rendering -> Distortion
    - RagCam -> Rendering -> Rough Distortion
    - RagCam -> Rendering -> Post-process
    - RagCam -> Rendering -> Custom Post-process
    - RagCam -> Rendering -> Stop NaN
    - RagCam -> Rendering -> Depth Of Field
    - RagCam -> Rendering -> Motion Blur
    - RagCam -> Rendering -> Panini Projection
    - RagCam -> Rendering -> Bloom
    - RagCam -> Rendering -> Screen Space Lens Flare
    - RagCam -> Rendering -> Lens Distortion
    - RagCam -> Rendering -> Chromatic Aberration
    - RagCam -> Rendering -> Vignette
    - RagCam -> Rendering -> Color Grading
    - RagCam -> Rendering -> Film Grain
    - RagCam -> Rendering -> Dithering
    - RagCam -> Rendering -> Anti-aliasing
    - RagCam -> Rendering -> Tonemapping
    - RagCam -> Rendering -> Lens Flare Data Driven
    - RagCam -> Rendering -> After Post-process
    - RagCam -> Rendering -> Depth Test
    - RagCam -> Rendering -> Virtual Texturing
    - RagCam -> Rendering -> Asymmetric Projection
    - RagCam -> Rendering -> Screen Coordinates Override
    - RagCam -> Rendering -> High Quality Line Rendering
    - RagCam -> Lighting
    - RagCam -> Lighting -> Shadow Maps
    - RagCam -> Lighting -> Contact Shadows
    - RagCam -> Lighting -> Adaptive Probe Volumes
    - RagCam -> Lighting -> Normalize Reflection Probes
    - RagCam -> Lighting -> Screen Space Shadows
    - RagCam -> Lighting -> Shadowmask
    - RagCam -> Lighting -> Screen Space Reflection
    - RagCam -> Lighting -> Transparents
    - RagCam -> Lighting -> Screen Space Global Illumination
    - RagCam -> Lighting -> Screen Space Ambient Occlusion
    - RagCam -> Lighting -> Transmission
    - RagCam -> Lighting -> Fog
    - RagCam -> Lighting -> Volumetric Fog
    - RagCam -> Lighting -> Reprojection
    - RagCam -> Lighting -> Light Layers
    - RagCam -> Lighting -> Exposure Control
    - RagCam -> Lighting -> Reflection Probe
    - RagCam -> Lighting -> Planar Reflection Probe
    - RagCam -> Lighting -> Metallic Indirect Fallback
    - RagCam -> Lighting -> Sky Reflection
    - RagCam -> Lighting -> Direct Specular Lighting
    - RagCam -> Lighting -> Subsurface Scattering
    - RagCam -> Lighting -> Volumetric Clouds
    - RagCam -> Lighting -> Full Resolution Clouds For Sky
    - RagCam -> Async Compute
    - RagCam -> Async Compute -> Asynchronous Execution
    - RagCam -> Async Compute -> Light List
    - RagCam -> Async Compute -> SS Reflection
    - RagCam -> Async Compute -> SS Ambient Occlusion
    - RagCam -> Async Compute -> Contact Shadows
    - RagCam -> Async Compute -> Volume Voxelizations
    - RagCam -> Async Compute -> High Quality Line Rendering
    - RagCam -> Light Loop
    - RagCam -> Light Loop -> FPTL For Forward Opaque
    - RagCam -> Light Loop -> Big Tile Prepass
    - RagCam -> Light Loop -> Compute Light Variants
    - RagCam -> Light Loop -> Compute Material Variants
    - Camera -> Rendering
    - Camera -> Rendering -> Lit Shader Mode
    - Camera -> Rendering -> Clear GBuffers
    - Camera -> Rendering -> Full Depth Prepass within Deferred
    - Camera -> Rendering -> Opaque Objects
    - Camera -> Rendering -> Transparent Objects
    - Camera -> Rendering -> Transparent Prepass
    - Camera -> Rendering -> Transparent Postpass
    - Camera -> Rendering -> Low Resolution Transparent
    - Camera -> Rendering -> Refraction
    - Camera -> Rendering -> Water
    - Camera -> Rendering -> Water Exclusion
    - Camera -> Rendering -> Water Decals
    - Camera -> Rendering -> Compute Thickness
    - Camera -> Rendering -> Decals
    - Camera -> Rendering -> Decal Layers
    - Camera -> Rendering -> Rendering Layer Mask Buffer
    - Camera -> Rendering -> Ray Tracing
    - Camera -> Rendering -> RaytracingVFX
    - Camera -> Rendering -> Custom Pass
    - Camera -> Rendering -> Variable Rate Shading
    - Camera -> Rendering -> Motion Vectors
    - Camera -> Rendering -> Opaque Object Motion
    - Camera -> Rendering -> Transparent Object Motion
    - Camera -> Rendering -> Distortion
    - Camera -> Rendering -> Rough Distortion
    - Camera -> Rendering -> Post-process
    - Camera -> Rendering -> Custom Post-process
    - Camera -> Rendering -> Stop NaN
    - Camera -> Rendering -> Depth Of Field
    - Camera -> Rendering -> Motion Blur
    - Camera -> Rendering -> Panini Projection
    - Camera -> Rendering -> Bloom
    - Camera -> Rendering -> Screen Space Lens Flare
    - Camera -> Rendering -> Lens Distortion
    - Camera -> Rendering -> Chromatic Aberration
    - Camera -> Rendering -> Vignette
    - Camera -> Rendering -> Color Grading
    - Camera -> Rendering -> Film Grain
    - Camera -> Rendering -> Dithering
    - Camera -> Rendering -> Anti-aliasing
    - Camera -> Rendering -> Tonemapping
    - Camera -> Rendering -> Lens Flare Data Driven
    - Camera -> Rendering -> After Post-process
    - Camera -> Rendering -> Depth Test
    - Camera -> Rendering -> Virtual Texturing
    - Camera -> Rendering -> Asymmetric Projection
    - Camera -> Rendering -> Screen Coordinates Override
    - Camera -> Rendering -> High Quality Line Rendering
    - Camera -> Lighting
    - Camera -> Lighting -> Shadow Maps
    - Camera -> Lighting -> Contact Shadows
    - Camera -> Lighting -> Adaptive Probe Volumes
    - Camera -> Lighting -> Normalize Reflection Probes
    - Camera -> Lighting -> Screen Space Shadows
    - Camera -> Lighting -> Shadowmask
    - Camera -> Lighting -> Screen Space Reflection
    - Camera -> Lighting -> Transparents
    - Camera -> Lighting -> Screen Space Global Illumination
    - Camera -> Lighting -> Screen Space Ambient Occlusion
    - Camera -> Lighting -> Transmission
    - Camera -> Lighting -> Fog
    - Camera -> Lighting -> Volumetric Fog
    - Camera -> Lighting -> Reprojection
    - Camera -> Lighting -> Light Layers
    - Camera -> Lighting -> Exposure Control
    - Camera -> Lighting -> Reflection Probe
    - Camera -> Lighting -> Planar Reflection Probe
    - Camera -> Lighting -> Metallic Indirect Fallback
    - Camera -> Lighting -> Sky Reflection
    - Camera -> Lighting -> Direct Specular Lighting
    - Camera -> Lighting -> Subsurface Scattering
    - Camera -> Lighting -> Volumetric Clouds
    - Camera -> Lighting -> Full Resolution Clouds For Sky
    - Camera -> Async Compute
    - Camera -> Async Compute -> Asynchronous Execution
    - Camera -> Async Compute -> Light List
    - Camera -> Async Compute -> SS Reflection
    - Camera -> Async Compute -> SS Ambient Occlusion
    - Camera -> Async Compute -> Contact Shadows
    - Camera -> Async Compute -> Volume Voxelizations
    - Camera -> Async Compute -> High Quality Line Rendering
    - Camera -> Light Loop
    - Camera -> Light Loop -> FPTL For Forward Opaque
    - Camera -> Light Loop -> Big Tile Prepass
    - Camera -> Light Loop -> Compute Light Variants
    - Camera -> Light Loop -> Compute Material Variants
    m_Values:
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
  m_Settings: {fileID: 0}
